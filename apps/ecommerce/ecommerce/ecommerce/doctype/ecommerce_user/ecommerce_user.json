{"actions": [], "allow_rename": 1, "autoname": "field:user", "creation": "2025-07-01 10:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["user", "user_type", "account_tier", "verification_status", "section_break_5", "nida_number", "business_license", "business_license_number", "column_break_9", "phone_number", "location_details", "address_line_1", "address_line_2", "city", "state", "postal_code", "country", "section_break_17", "company_name", "business_type", "tax_id", "column_break_21", "website", "social_media_links", "preferred_language", "section_break_25", "account_balance", "credit_limit", "loyalty_points", "column_break_29", "total_orders", "total_spent", "last_order_date", "section_break_33", "verification_documents", "notes", "is_active"], "fields": [{"fieldname": "user", "fieldtype": "Link", "in_list_view": 1, "label": "User", "options": "User", "reqd": 1, "unique": 1}, {"fieldname": "user_type", "fieldtype": "Select", "in_list_view": 1, "label": "User Type", "options": "Customer\nCompany\nWholesaler\nRetailer\nDelivery Person\nMiddleman", "reqd": 1}, {"fieldname": "account_tier", "fieldtype": "Select", "label": "Account Tier", "options": "Basic\nPremium\nEnterprise", "default": "Basic"}, {"fieldname": "verification_status", "fieldtype": "Select", "in_list_view": 1, "label": "Verification Status", "options": "Pending\nVerified\nRejected", "default": "Pending"}, {"fieldname": "section_break_5", "fieldtype": "Section Break", "label": "Verification Details"}, {"fieldname": "nida_number", "fieldtype": "Data", "label": "NIDA Number", "description": "National ID Number for verification"}, {"fieldname": "business_license", "fieldtype": "Attach", "label": "Business License"}, {"fieldname": "business_license_number", "fieldtype": "Data", "label": "Business License Number"}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"fieldname": "phone_number", "fieldtype": "Data", "label": "Phone Number"}, {"fieldname": "location_details", "fieldtype": "JSON", "label": "Location Details", "description": "GPS coordinates and location information"}, {"fieldname": "address_line_1", "fieldtype": "Data", "label": "Address Line 1"}, {"fieldname": "address_line_2", "fieldtype": "Data", "label": "Address Line 2"}, {"fieldname": "city", "fieldtype": "Data", "label": "City"}, {"fieldname": "state", "fieldtype": "Data", "label": "State/Region"}, {"fieldname": "postal_code", "fieldtype": "Data", "label": "Postal Code"}, {"fieldname": "country", "fieldtype": "Link", "label": "Country", "options": "Country"}, {"fieldname": "section_break_17", "fieldtype": "Section Break", "label": "Business Information"}, {"fieldname": "company_name", "fieldtype": "Data", "label": "Company Name"}, {"fieldname": "business_type", "fieldtype": "Select", "label": "Business Type", "options": "Sole Proprietorship\nPartnership\nCorporation\nLLC\nNGO\nGovernment"}, {"fieldname": "tax_id", "fieldtype": "Data", "label": "Tax ID"}, {"fieldname": "column_break_21", "fieldtype": "Column Break"}, {"fieldname": "website", "fieldtype": "Data", "label": "Website"}, {"fieldname": "social_media_links", "fieldtype": "JSON", "label": "Social Media Links"}, {"fieldname": "preferred_language", "fieldtype": "Select", "label": "Preferred Language", "options": "English\nSwahili\nFrench\nArabic", "default": "English"}, {"fieldname": "section_break_25", "fieldtype": "Section Break", "label": "Account Information"}, {"fieldname": "account_balance", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Account <PERSON><PERSON>", "default": "0.0"}, {"fieldname": "credit_limit", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Credit Limit", "default": "0.0"}, {"fieldname": "loyalty_points", "fieldtype": "Int", "label": "Loyalty Points", "default": "0"}, {"fieldname": "column_break_29", "fieldtype": "Column Break"}, {"fieldname": "total_orders", "fieldtype": "Int", "label": "Total Orders", "default": "0", "read_only": 1}, {"fieldname": "total_spent", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Spent", "default": "0.0", "read_only": 1}, {"fieldname": "last_order_date", "fieldtype": "Date", "label": "Last Order Date", "read_only": 1}, {"fieldname": "section_break_33", "fieldtype": "Section Break", "label": "Additional Information"}, {"fieldname": "verification_documents", "fieldtype": "JSON", "label": "Verification Documents", "description": "Additional verification documents"}, {"fieldname": "notes", "fieldtype": "Text", "label": "Notes"}, {"fieldname": "is_active", "fieldtype": "Check", "label": "Is Active", "default": "1"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-01 10:00:00.000000", "modified_by": "Administrator", "module": "Ecommerce", "name": "Ecommerce User", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Ecommerce Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "role": "Ecommerce User", "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}