# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import nowdate, flt
import json


class EcommerceUser(Document):
    def validate(self):
        """Validate the Ecommerce User document"""
        self.validate_user_type_requirements()
        self.validate_nida_number()
        self.validate_business_license()
        self.set_default_values()

    def validate_user_type_requirements(self):
        """Validate requirements based on user type"""
        if self.user_type == "Company":
            if not self.nida_number:
                frappe.throw("NIDA Number is required for Company accounts")
            if not self.business_license:
                frappe.throw("Business License is required for Company accounts")
            if not self.company_name:
                frappe.throw("Company Name is required for Company accounts")

        elif self.user_type in ["Wholesaler", "Retailer"]:
            if not self.business_license_number:
                frappe.throw(f"Business License Number is required for {self.user_type} accounts")

    def validate_nida_number(self):
        """Validate NIDA number format and uniqueness"""
        if self.nida_number:
            # Check if NIDA number already exists
            existing = frappe.db.exists("Ecommerce User", {
                "nida_number": self.nida_number,
                "name": ["!=", self.name]
            })
            if existing:
                frappe.throw("NIDA Number already exists for another user")

    def validate_business_license(self):
        """Validate business license requirements"""
        if self.business_license_number:
            # Check if business license number already exists
            existing = frappe.db.exists("Ecommerce User", {
                "business_license_number": self.business_license_number,
                "name": ["!=", self.name]
            })
            if existing:
                frappe.throw("Business License Number already exists for another user")

    def set_default_values(self):
        """Set default values for new users"""
        if not self.account_balance:
            self.account_balance = 0.0
        if not self.credit_limit:
            self.credit_limit = 0.0
        if not self.loyalty_points:
            self.loyalty_points = 0
        if not self.total_orders:
            self.total_orders = 0
        if not self.total_spent:
            self.total_spent = 0.0

    def on_update(self):
        """Actions to perform after updating the document"""
        self.update_user_roles()
        self.sync_with_customer_supplier()

    def update_user_roles(self):
        """Update user roles based on user type"""
        user_doc = frappe.get_doc("User", self.user)
        
        # Remove existing ecommerce roles
        ecommerce_roles = ["Ecommerce User", "Ecommerce Manager", "Customer", "Supplier"]
        for role in ecommerce_roles:
            if user_doc.has_role(role):
                user_doc.remove_roles(role)

        # Add appropriate roles based on user type
        if self.user_type == "Customer":
            user_doc.add_roles("Ecommerce User", "Customer")
        elif self.user_type in ["Company", "Wholesaler", "Retailer"]:
            user_doc.add_roles("Ecommerce User", "Supplier")
        elif self.user_type == "Delivery Person":
            user_doc.add_roles("Ecommerce User")
        elif self.user_type == "Middleman":
            user_doc.add_roles("Ecommerce User")

        user_doc.save(ignore_permissions=True)

    def sync_with_customer_supplier(self):
        """Sync with ERPNext Customer/Supplier doctypes"""
        user_doc = frappe.get_doc("User", self.user)
        
        if self.user_type == "Customer":
            self.create_or_update_customer(user_doc)
        elif self.user_type in ["Company", "Wholesaler", "Retailer"]:
            self.create_or_update_supplier(user_doc)

    def create_or_update_customer(self, user_doc):
        """Create or update Customer record in ERPNext"""
        customer_name = f"{user_doc.first_name} {user_doc.last_name or ''}".strip()
        
        if not frappe.db.exists("Customer", {"email_id": user_doc.email}):
            customer = frappe.get_doc({
                "doctype": "Customer",
                "customer_name": customer_name,
                "customer_type": "Individual",
                "customer_group": "Individual",
                "territory": "All Territories",
                "email_id": user_doc.email,
                "mobile_no": self.phone_number,
                "is_internal_customer": 0
            })
            customer.insert(ignore_permissions=True)
        else:
            customer = frappe.get_doc("Customer", {"email_id": user_doc.email})
            customer.customer_name = customer_name
            customer.mobile_no = self.phone_number
            customer.save(ignore_permissions=True)

    def create_or_update_supplier(self, user_doc):
        """Create or update Supplier record in ERPNext"""
        supplier_name = self.company_name or f"{user_doc.first_name} {user_doc.last_name or ''}".strip()
        
        if not frappe.db.exists("Supplier", {"email_id": user_doc.email}):
            supplier = frappe.get_doc({
                "doctype": "Supplier",
                "supplier_name": supplier_name,
                "supplier_type": "Company" if self.user_type == "Company" else "Individual",
                "supplier_group": "All Supplier Groups",
                "email_id": user_doc.email,
                "mobile_no": self.phone_number,
                "is_internal_supplier": 0,
                "tax_id": self.tax_id
            })
            supplier.insert(ignore_permissions=True)
        else:
            supplier = frappe.get_doc("Supplier", {"email_id": user_doc.email})
            supplier.supplier_name = supplier_name
            supplier.mobile_no = self.phone_number
            supplier.tax_id = self.tax_id
            supplier.save(ignore_permissions=True)

    def update_order_statistics(self, order_amount=0):
        """Update order statistics when a new order is placed"""
        self.total_orders = (self.total_orders or 0) + 1
        self.total_spent = flt(self.total_spent or 0) + flt(order_amount)
        self.last_order_date = nowdate()
        self.save(ignore_permissions=True)

    def add_loyalty_points(self, points):
        """Add loyalty points to user account"""
        self.loyalty_points = (self.loyalty_points or 0) + points
        self.save(ignore_permissions=True)

    def deduct_loyalty_points(self, points):
        """Deduct loyalty points from user account"""
        if (self.loyalty_points or 0) >= points:
            self.loyalty_points = (self.loyalty_points or 0) - points
            self.save(ignore_permissions=True)
            return True
        return False

    def get_location_coordinates(self):
        """Get GPS coordinates from location details"""
        if self.location_details:
            try:
                location_data = json.loads(self.location_details) if isinstance(self.location_details, str) else self.location_details
                return location_data.get("latitude"), location_data.get("longitude")
            except:
                return None, None
        return None, None

    def set_location_coordinates(self, latitude, longitude):
        """Set GPS coordinates in location details"""
        location_data = {
            "latitude": latitude,
            "longitude": longitude,
            "address": f"{self.address_line_1}, {self.city}, {self.country}"
        }
        self.location_details = json.dumps(location_data)


@frappe.whitelist()
def get_user_profile(user_email):
    """Get ecommerce user profile by email"""
    ecommerce_user = frappe.db.get_value("Ecommerce User", {"user": user_email}, "*")
    if ecommerce_user:
        return ecommerce_user
    return None


@frappe.whitelist()
def verify_user(user_name, verification_status, notes=None):
    """Verify or reject user verification"""
    if not frappe.has_permission("Ecommerce User", "write"):
        frappe.throw("Insufficient permissions")
    
    user_doc = frappe.get_doc("Ecommerce User", user_name)
    user_doc.verification_status = verification_status
    if notes:
        user_doc.notes = notes
    user_doc.save()
    
    return {"status": "success", "message": f"User {verification_status.lower()} successfully"}


@frappe.whitelist()
def get_user_statistics(user_type=None):
    """Get user statistics for dashboard"""
    filters = {}
    if user_type:
        filters["user_type"] = user_type
    
    total_users = frappe.db.count("Ecommerce User", filters)
    verified_users = frappe.db.count("Ecommerce User", {**filters, "verification_status": "Verified"})
    pending_users = frappe.db.count("Ecommerce User", {**filters, "verification_status": "Pending"})
    
    return {
        "total_users": total_users,
        "verified_users": verified_users,
        "pending_users": pending_users,
        "verification_rate": (verified_users / total_users * 100) if total_users > 0 else 0
    }
