# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
import unittest
from frappe.tests.utils import FrappeTestCase


class TestEcommerceUser(FrappeTestCase):
    def setUp(self):
        """Set up test data"""
        # Create a test user if it doesn't exist
        if not frappe.db.exists("User", "<EMAIL>"):
            user = frappe.get_doc({
                "doctype": "User",
                "email": "<EMAIL>",
                "first_name": "Test",
                "last_name": "User",
                "send_welcome_email": 0
            })
            user.insert(ignore_permissions=True)

    def test_create_customer_user(self):
        """Test creating a customer user"""
        ecommerce_user = frappe.get_doc({
            "doctype": "Ecommerce User",
            "user": "<EMAIL>",
            "user_type": "Customer",
            "phone_number": "+************",
            "city": "Dar es Salaam",
            "country": "Tanzania"
        })
        ecommerce_user.insert()
        
        self.assertEqual(ecommerce_user.user_type, "Customer")
        self.assertEqual(ecommerce_user.verification_status, "Pending")
        self.assertEqual(ecommerce_user.account_tier, "Basic")

    def test_create_company_user_validation(self):
        """Test validation for company user requirements"""
        with self.assertRaises(frappe.ValidationError):
            ecommerce_user = frappe.get_doc({
                "doctype": "Ecommerce User",
                "user": "<EMAIL>",
                "user_type": "Company",
                "phone_number": "+************"
                # Missing required fields: nida_number, business_license, company_name
            })
            ecommerce_user.insert()

    def test_nida_number_uniqueness(self):
        """Test NIDA number uniqueness validation"""
        # Create first user with NIDA
        user1 = frappe.get_doc({
            "doctype": "Ecommerce User",
            "user": "<EMAIL>",
            "user_type": "Company",
            "nida_number": "12345678901234567890",
            "company_name": "Test Company",
            "phone_number": "+************"
        })
        user1.insert()

        # Try to create second user with same NIDA
        if not frappe.db.exists("User", "<EMAIL>"):
            user2_doc = frappe.get_doc({
                "doctype": "User",
                "email": "<EMAIL>",
                "first_name": "Test2",
                "last_name": "User",
                "send_welcome_email": 0
            })
            user2_doc.insert(ignore_permissions=True)

        with self.assertRaises(frappe.ValidationError):
            user2 = frappe.get_doc({
                "doctype": "Ecommerce User",
                "user": "<EMAIL>",
                "user_type": "Company",
                "nida_number": "12345678901234567890",  # Same NIDA
                "company_name": "Test Company 2",
                "phone_number": "+255123456790"
            })
            user2.insert()

    def test_loyalty_points_operations(self):
        """Test loyalty points add and deduct operations"""
        ecommerce_user = frappe.get_doc({
            "doctype": "Ecommerce User",
            "user": "<EMAIL>",
            "user_type": "Customer",
            "phone_number": "+************"
        })
        ecommerce_user.insert()

        # Test adding loyalty points
        ecommerce_user.add_loyalty_points(100)
        self.assertEqual(ecommerce_user.loyalty_points, 100)

        # Test deducting loyalty points (sufficient balance)
        result = ecommerce_user.deduct_loyalty_points(50)
        self.assertTrue(result)
        self.assertEqual(ecommerce_user.loyalty_points, 50)

        # Test deducting loyalty points (insufficient balance)
        result = ecommerce_user.deduct_loyalty_points(100)
        self.assertFalse(result)
        self.assertEqual(ecommerce_user.loyalty_points, 50)

    def test_order_statistics_update(self):
        """Test updating order statistics"""
        ecommerce_user = frappe.get_doc({
            "doctype": "Ecommerce User",
            "user": "<EMAIL>",
            "user_type": "Customer",
            "phone_number": "+************"
        })
        ecommerce_user.insert()

        # Update order statistics
        ecommerce_user.update_order_statistics(order_amount=1000)
        
        self.assertEqual(ecommerce_user.total_orders, 1)
        self.assertEqual(ecommerce_user.total_spent, 1000)
        self.assertIsNotNone(ecommerce_user.last_order_date)

    def test_location_coordinates(self):
        """Test location coordinates functionality"""
        ecommerce_user = frappe.get_doc({
            "doctype": "Ecommerce User",
            "user": "<EMAIL>",
            "user_type": "Customer",
            "phone_number": "+************"
        })
        ecommerce_user.insert()

        # Set location coordinates
        ecommerce_user.set_location_coordinates(-6.7924, 39.2083)
        
        # Get location coordinates
        lat, lng = ecommerce_user.get_location_coordinates()
        self.assertEqual(lat, -6.7924)
        self.assertEqual(lng, 39.2083)

    def tearDown(self):
        """Clean up test data"""
        # Delete test ecommerce users
        frappe.db.delete("Ecommerce User", {"user": "<EMAIL>"})
        frappe.db.delete("Ecommerce User", {"user": "<EMAIL>"})
        
        # Delete test users
        frappe.db.delete("User", {"email": "<EMAIL>"})
        frappe.db.delete("User", {"email": "<EMAIL>"})
        
        frappe.db.commit()


if __name__ == "__main__":
    unittest.main()
