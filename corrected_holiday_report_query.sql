-- Corrected SQL Query for Holiday Report with Working Hours
-- This query properly fetches working hours from attendance records

SELECT
    h.employee AS "Employee ID:Link/Employee:120",
    e.employee_name AS "Employee Name::150",
    e.department AS "Department::120",
    e.designation AS "Designation::120",
    ROUND(SUM(CASE WHEN h.type = 'Given' THEN (h.hours + h.minutes / 60.0) ELSE 0 END), 2) AS "Holiday Given (hrs):Float:130",
    ROUND(SUM(CASE WHEN h.type = 'Taken' THEN (h.hours + h.minutes / 60.0) ELSE 0 END), 2) AS "Holiday Taken (hrs):Float:130",
    ROUND(
        SUM(CASE WHEN h.type = 'Given' THEN (h.hours + h.minutes / 60.0) ELSE 0 END)
        - SUM(CASE WHEN h.type = 'Taken' THEN (h.hours + h.minutes / 60.0) ELSE 0 END),
        2
    ) AS "Balance (hrs):Float:120",
    h.hours AS "Hours::120",
    ROUND(AVG(a.working_hours), 2) AS "Average Working Hours::120",
    ROUND(SUM(a.working_hours), 2) AS "Total Working Hours::120"
FROM
    `tabVelocetec Holiday` h
LEFT JOIN
    `tabEmployee` e ON e.name = h.employee
LEFT JOIN
    `tabAttendance` a ON a.employee = h.employee 
    AND a.attendance_date BETWEEN %(from_date)s AND %(to_date)s
    AND a.docstatus = 1
    AND a.status IN ('Present', 'Half Day', 'Work From Home')
WHERE
    h.date BETWEEN %(from_date)s AND %(to_date)s
GROUP BY
    h.employee

-- Alternative query if you want working hours for specific dates in the holiday records:
-- 
-- SELECT
--     h.employee AS "Employee ID:Link/Employee:120",
--     e.employee_name AS "Employee Name::150",
--     e.department AS "Department::120",
--     e.designation AS "Designation::120",
--     ROUND(SUM(CASE WHEN h.type = 'Given' THEN (h.hours + h.minutes / 60.0) ELSE 0 END), 2) AS "Holiday Given (hrs):Float:130",
--     ROUND(SUM(CASE WHEN h.type = 'Taken' THEN (h.hours + h.minutes / 60.0) ELSE 0 END), 2) AS "Holiday Taken (hrs):Float:130",
--     ROUND(
--         SUM(CASE WHEN h.type = 'Given' THEN (h.hours + h.minutes / 60.0) ELSE 0 END)
--         - SUM(CASE WHEN h.type = 'Taken' THEN (h.hours + h.minutes / 60.0) ELSE 0 END),
--         2
--     ) AS "Balance (hrs):Float:120",
--     h.hours AS "Hours::120",
--     COALESCE(a.working_hours, 0) AS "Working Hours on Holiday Date::120"
-- FROM
--     `tabVelocetec Holiday` h
-- LEFT JOIN
--     `tabEmployee` e ON e.name = h.employee
-- LEFT JOIN
--     `tabAttendance` a ON a.employee = h.employee 
--     AND a.attendance_date = h.date
--     AND a.docstatus = 1
-- WHERE
--     h.date BETWEEN %(from_date)s AND %(to_date)s
-- GROUP BY
--     h.employee, h.date, a.working_hours
-- ORDER BY
--     h.employee, h.date
