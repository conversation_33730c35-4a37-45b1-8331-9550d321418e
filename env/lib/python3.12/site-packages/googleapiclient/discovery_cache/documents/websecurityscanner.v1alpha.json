{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://websecurityscanner.googleapis.com/", "batchPath": "batch", "canonicalName": "WebSecurityScanner", "description": "Scans your Compute and App Engine apps for common web vulnerabilities.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/security-command-center/docs/concepts-web-security-scanner-overview/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "websecurityscanner:v1alpha", "kind": "discovery#restDescription", "mtlsRootUrl": "https://websecurityscanner.mtls.googleapis.com/", "name": "websecurityscanner", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"scanConfigs": {"methods": {"create": {"description": "Creates a new ScanConfig.", "flatPath": "v1alpha/projects/{projectsId}/scanConfigs", "httpMethod": "POST", "id": "websecurityscanner.projects.scanConfigs.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource name where the scan is created, which should be a project resource name in the format 'projects/{projectId}'.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/scanConfigs", "request": {"$ref": "ScanConfig"}, "response": {"$ref": "ScanConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an existing ScanConfig and its child resources.", "flatPath": "v1alpha/projects/{projectsId}/scanConfigs/{scanConfigsId}", "httpMethod": "DELETE", "id": "websecurityscanner.projects.scanConfigs.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the ScanConfig to be deleted. The name follows the format of 'projects/{projectId}/scanConfigs/{scanConfigId}'.", "location": "path", "pattern": "^projects/[^/]+/scanConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a ScanConfig.", "flatPath": "v1alpha/projects/{projectsId}/scanConfigs/{scanConfigsId}", "httpMethod": "GET", "id": "websecurityscanner.projects.scanConfigs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the ScanConfig to be returned. The name follows the format of 'projects/{projectId}/scanConfigs/{scanConfigId}'.", "location": "path", "pattern": "^projects/[^/]+/scanConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "ScanConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists ScanConfigs under a given project.", "flatPath": "v1alpha/projects/{projectsId}/scanConfigs", "httpMethod": "GET", "id": "websecurityscanner.projects.scanConfigs.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of ScanConfigs to return, can be limited by server. If not specified or not positive, the implementation will select a reasonable value.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results to be returned. This should be a `next_page_token` value returned from a previous List request. If unspecified, the first page of results is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource name, which should be a project resource name in the format 'projects/{projectId}'.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/scanConfigs", "response": {"$ref": "ListScanConfigsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a ScanConfig. This method support partial update of a ScanConfig.", "flatPath": "v1alpha/projects/{projectsId}/scanConfigs/{scanConfigsId}", "httpMethod": "PATCH", "id": "websecurityscanner.projects.scanConfigs.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the ScanConfig. The name follows the format of 'projects/{projectId}/scanConfigs/{scanConfigId}'. The ScanConfig IDs are generated by the system.", "location": "path", "pattern": "^projects/[^/]+/scanConfigs/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The update mask applies to the resource. For the `FieldMask` definition, see https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "ScanConfig"}, "response": {"$ref": "ScanConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "start": {"description": "Start a ScanRun according to the given ScanConfig.", "flatPath": "v1alpha/projects/{projectsId}/scanConfigs/{scanConfigsId}:start", "httpMethod": "POST", "id": "websecurityscanner.projects.scanConfigs.start", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the ScanConfig to be used. The name follows the format of 'projects/{projectId}/scanConfigs/{scanConfigId}'.", "location": "path", "pattern": "^projects/[^/]+/scanConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:start", "request": {"$ref": "StartScanRunRequest"}, "response": {"$ref": "ScanRun"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"scanRuns": {"methods": {"get": {"description": "Gets a ScanRun.", "flatPath": "v1alpha/projects/{projectsId}/scanConfigs/{scanConfigsId}/scanRuns/{scanRunsId}", "httpMethod": "GET", "id": "websecurityscanner.projects.scanConfigs.scanRuns.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the ScanRun to be returned. The name follows the format of 'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.", "location": "path", "pattern": "^projects/[^/]+/scanConfigs/[^/]+/scanRuns/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "ScanRun"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists ScanRuns under a given ScanConfig, in descending order of ScanRun stop time.", "flatPath": "v1alpha/projects/{projectsId}/scanConfigs/{scanConfigsId}/scanRuns", "httpMethod": "GET", "id": "websecurityscanner.projects.scanConfigs.scanRuns.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of ScanRuns to return, can be limited by server. If not specified or not positive, the implementation will select a reasonable value.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results to be returned. This should be a `next_page_token` value returned from a previous List request. If unspecified, the first page of results is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource name, which should be a scan resource name in the format 'projects/{projectId}/scanConfigs/{scanConfigId}'.", "location": "path", "pattern": "^projects/[^/]+/scanConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/scanRuns", "response": {"$ref": "ListScanRunsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "stop": {"description": "Stops a ScanRun. The stopped ScanRun is returned.", "flatPath": "v1alpha/projects/{projectsId}/scanConfigs/{scanConfigsId}/scanRuns/{scanRunsId}:stop", "httpMethod": "POST", "id": "websecurityscanner.projects.scanConfigs.scanRuns.stop", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the ScanRun to be stopped. The name follows the format of 'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.", "location": "path", "pattern": "^projects/[^/]+/scanConfigs/[^/]+/scanRuns/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:stop", "request": {"$ref": "StopScanRunRequest"}, "response": {"$ref": "ScanRun"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"crawledUrls": {"methods": {"list": {"description": "List CrawledUrls under a given ScanRun.", "flatPath": "v1alpha/projects/{projectsId}/scanConfigs/{scanConfigsId}/scanRuns/{scanRunsId}/crawledUrls", "httpMethod": "GET", "id": "websecurityscanner.projects.scanConfigs.scanRuns.crawledUrls.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of CrawledUrls to return, can be limited by server. If not specified or not positive, the implementation will select a reasonable value.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results to be returned. This should be a `next_page_token` value returned from a previous List request. If unspecified, the first page of results is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource name, which should be a scan run resource name in the format 'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.", "location": "path", "pattern": "^projects/[^/]+/scanConfigs/[^/]+/scanRuns/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/crawledUrls", "response": {"$ref": "ListCrawledUrlsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "findingTypeStats": {"methods": {"list": {"description": "List all FindingTypeStats under a given ScanRun.", "flatPath": "v1alpha/projects/{projectsId}/scanConfigs/{scanConfigsId}/scanRuns/{scanRunsId}/findingTypeStats", "httpMethod": "GET", "id": "websecurityscanner.projects.scanConfigs.scanRuns.findingTypeStats.list", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource name, which should be a scan run resource name in the format 'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.", "location": "path", "pattern": "^projects/[^/]+/scanConfigs/[^/]+/scanRuns/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/findingTypeStats", "response": {"$ref": "ListFindingTypeStatsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "findings": {"methods": {"get": {"description": "Gets a Finding.", "flatPath": "v1alpha/projects/{projectsId}/scanConfigs/{scanConfigsId}/scanRuns/{scanRunsId}/findings/{findingsId}", "httpMethod": "GET", "id": "websecurityscanner.projects.scanConfigs.scanRuns.findings.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Finding to be returned. The name follows the format of 'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}/findings/{findingId}'.", "location": "path", "pattern": "^projects/[^/]+/scanConfigs/[^/]+/scanRuns/[^/]+/findings/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Finding"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List Findings under a given ScanRun.", "flatPath": "v1alpha/projects/{projectsId}/scanConfigs/{scanConfigsId}/scanRuns/{scanRunsId}/findings", "httpMethod": "GET", "id": "websecurityscanner.projects.scanConfigs.scanRuns.findings.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Required. The filter expression. The expression must be in the format: . Supported field: 'finding_type'. Supported operator: '='.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of Findings to return, can be limited by server. If not specified or not positive, the implementation will select a reasonable value.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results to be returned. This should be a `next_page_token` value returned from a previous List request. If unspecified, the first page of results is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource name, which should be a scan run resource name in the format 'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.", "location": "path", "pattern": "^projects/[^/]+/scanConfigs/[^/]+/scanRuns/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/findings", "response": {"$ref": "ListFindingsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}}}, "revision": "********", "rootUrl": "https://websecurityscanner.googleapis.com/", "schemas": {"Authentication": {"description": "Scan authentication configuration.", "id": "Authentication", "properties": {"customAccount": {"$ref": "CustomAccount", "description": "Authentication using a custom account."}, "googleAccount": {"$ref": "GoogleAccount", "description": "Authentication using a Google account."}}, "type": "object"}, "CrawledUrl": {"description": "A CrawledUrl resource represents a URL that was crawled during a ScanRun. Web Security Scanner Service crawls the web applications, following all links within the scope of sites, to find the URLs to test against.", "id": "CrawledUrl", "properties": {"body": {"description": "Output only. The body of the request that was used to visit the URL.", "type": "string"}, "httpMethod": {"description": "Output only. The http method of the request that was used to visit the URL, in uppercase.", "type": "string"}, "url": {"description": "Output only. The URL that was crawled.", "type": "string"}}, "type": "object"}, "CustomAccount": {"description": "Describes authentication configuration that uses a custom account.", "id": "CustomAccount", "properties": {"loginUrl": {"description": "Required. The login form URL of the website.", "type": "string"}, "password": {"description": "Required. Input only. The password of the custom account. The credential is stored encrypted and not returned in any response nor included in audit logs.", "type": "string"}, "username": {"description": "Required. The user name of the custom account.", "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Finding": {"description": "A Finding resource represents a vulnerability instance identified during a ScanRun.", "id": "Finding", "properties": {"body": {"description": "The body of the request that triggered the vulnerability.", "type": "string"}, "description": {"description": "The description of the vulnerability.", "type": "string"}, "finalUrl": {"description": "The URL where the browser lands when the vulnerability is detected.", "type": "string"}, "findingType": {"description": "The type of the Finding.", "enum": ["FINDING_TYPE_UNSPECIFIED", "MIXED_CONTENT", "OUTDATED_LIBRARY", "ROSETTA_FLASH", "XSS_CALLBACK", "XSS_ERROR", "CLEAR_TEXT_PASSWORD", "INVALID_CONTENT_TYPE", "XSS_ANGULAR_CALLBACK", "INVALID_HEADER", "MISSPELLED_SECURITY_HEADER_NAME", "MISMATCHING_SECURITY_HEADER_VALUES", "ACCESSIBLE_GIT_REPOSITORY", "ACCESSIBLE_SVN_REPOSITORY", "ACCESSIBLE_ENV_FILE"], "enumDescriptions": ["The invalid finding type.", "A page that was served over HTTPS also resources over HTTP. A man-in-the-middle attacker could tamper with the HTTP resource and gain full access to the website that loads the resource or to monitor the actions taken by the user.", "The version of an included library is known to contain a security issue. The scanner checks the version of library in use against a known list of vulnerable libraries. False positives are possible if the version detection fails or if the library has been manually patched.", "This type of vulnerability occurs when the value of a request parameter is reflected at the beginning of the response, for example, in requests using JSONP. Under certain circumstances, an attacker may be able to supply an alphanumeric-only Flash file in the vulnerable parameter causing the browser to execute the Flash file as if it originated on the vulnerable server.", "A cross-site scripting (XSS) bug is found via JavaScript callback. For detailed explanations on XSS, see https://www.google.com/about/appsecurity/learning/xss/.", "A potential cross-site scripting (XSS) bug due to JavaScript breakage. In some circumstances, the application under test might modify the test string before it is parsed by the browser. When the browser attempts to runs this modified test string, it will likely break and throw a JavaScript execution error, thus an injection issue is occurring. However, it may not be exploitable. Manual verification is needed to see if the test string modifications can be evaded and confirm that the issue is in fact an XSS vulnerability. For detailed explanations on XSS, see https://www.google.com/about/appsecurity/learning/xss/.", "An application appears to be transmitting a password field in clear text. An attacker can eavesdrop network traffic and sniff the password field.", "An application returns sensitive content with an invalid content type, or without an 'X-Content-Type-Options: nosniff' header.", "A cross-site scripting (XSS) vulnerability in AngularJS module that occurs when a user-provided string is interpolated by Angular.", "A malformed or invalid valued header.", "Misspelled security header name.", "Mismatching values in a duplicate security header.", "A world-readable git repository that potentially leaks source code, commit history or sensitive information such as credentials.", "A world-readable subversion repository that potentially leaks source code, commit history or sensitive information such as credentials.", "A world-readable env file that potentially leaks source code, commit history or sensitive information such as credentials."], "type": "string"}, "frameUrl": {"description": "If the vulnerability was originated from nested IFrame, the immediate parent IFrame is reported.", "type": "string"}, "fuzzedUrl": {"description": "The URL produced by the server-side fuzzer and used in the request that triggered the vulnerability.", "type": "string"}, "httpMethod": {"description": "The http method of the request that triggered the vulnerability, in uppercase.", "type": "string"}, "name": {"description": "The resource name of the Finding. The name follows the format of 'projects/{projectId}/scanConfigs/{scanConfigId}/scanruns/{scanRunId}/findings/{findingId}'. The finding IDs are generated by the system.", "type": "string"}, "outdatedLibrary": {"$ref": "OutdatedLibrary", "description": "An addon containing information about outdated libraries."}, "reproductionUrl": {"description": "The URL containing human-readable payload that user can leverage to reproduce the vulnerability.", "type": "string"}, "trackingId": {"description": "The tracking ID uniquely identifies a vulnerability instance across multiple ScanRuns.", "type": "string"}, "violatingResource": {"$ref": "ViolatingResource", "description": "An addon containing detailed information regarding any resource causing the vulnerability such as JavaScript sources, image, audio files, etc."}, "vulnerableHeaders": {"$ref": "VulnerableHeaders", "description": "An addon containing information about vulnerable or missing HTTP headers."}, "vulnerableParameters": {"$ref": "VulnerableParameters", "description": "An addon containing information about request parameters which were found to be vulnerable."}, "xss": {"$ref": "Xss", "description": "An addon containing information reported for an XSS, if any."}}, "type": "object"}, "FindingTypeStats": {"description": "A FindingTypeStats resource represents stats regarding a specific FindingType of Findings under a given ScanRun.", "id": "FindingTypeStats", "properties": {"findingCount": {"description": "The count of findings belonging to this finding type.", "format": "int32", "type": "integer"}, "findingType": {"description": "The finding type associated with the stats.", "enum": ["FINDING_TYPE_UNSPECIFIED", "MIXED_CONTENT", "OUTDATED_LIBRARY", "ROSETTA_FLASH", "XSS_CALLBACK", "XSS_ERROR", "CLEAR_TEXT_PASSWORD", "INVALID_CONTENT_TYPE", "XSS_ANGULAR_CALLBACK", "INVALID_HEADER", "MISSPELLED_SECURITY_HEADER_NAME", "MISMATCHING_SECURITY_HEADER_VALUES", "ACCESSIBLE_GIT_REPOSITORY", "ACCESSIBLE_SVN_REPOSITORY", "ACCESSIBLE_ENV_FILE"], "enumDescriptions": ["The invalid finding type.", "A page that was served over HTTPS also resources over HTTP. A man-in-the-middle attacker could tamper with the HTTP resource and gain full access to the website that loads the resource or to monitor the actions taken by the user.", "The version of an included library is known to contain a security issue. The scanner checks the version of library in use against a known list of vulnerable libraries. False positives are possible if the version detection fails or if the library has been manually patched.", "This type of vulnerability occurs when the value of a request parameter is reflected at the beginning of the response, for example, in requests using JSONP. Under certain circumstances, an attacker may be able to supply an alphanumeric-only Flash file in the vulnerable parameter causing the browser to execute the Flash file as if it originated on the vulnerable server.", "A cross-site scripting (XSS) bug is found via JavaScript callback. For detailed explanations on XSS, see https://www.google.com/about/appsecurity/learning/xss/.", "A potential cross-site scripting (XSS) bug due to JavaScript breakage. In some circumstances, the application under test might modify the test string before it is parsed by the browser. When the browser attempts to runs this modified test string, it will likely break and throw a JavaScript execution error, thus an injection issue is occurring. However, it may not be exploitable. Manual verification is needed to see if the test string modifications can be evaded and confirm that the issue is in fact an XSS vulnerability. For detailed explanations on XSS, see https://www.google.com/about/appsecurity/learning/xss/.", "An application appears to be transmitting a password field in clear text. An attacker can eavesdrop network traffic and sniff the password field.", "An application returns sensitive content with an invalid content type, or without an 'X-Content-Type-Options: nosniff' header.", "A cross-site scripting (XSS) vulnerability in AngularJS module that occurs when a user-provided string is interpolated by Angular.", "A malformed or invalid valued header.", "Misspelled security header name.", "Mismatching values in a duplicate security header.", "A world-readable git repository that potentially leaks source code, commit history or sensitive information such as credentials.", "A world-readable subversion repository that potentially leaks source code, commit history or sensitive information such as credentials.", "A world-readable env file that potentially leaks source code, commit history or sensitive information such as credentials."], "type": "string"}}, "type": "object"}, "GoogleAccount": {"description": "Describes authentication configuration that uses a Google account.", "id": "GoogleAccount", "properties": {"password": {"description": "Required. Input only. The password of the Google account. The credential is stored encrypted and not returned in any response nor included in audit logs.", "type": "string"}, "username": {"description": "Required. The user name of the Google account.", "type": "string"}}, "type": "object"}, "Header": {"description": "Describes a HTTP Header.", "id": "Header", "properties": {"name": {"description": "Header name.", "type": "string"}, "value": {"description": "Header value.", "type": "string"}}, "type": "object"}, "ListCrawledUrlsResponse": {"description": "Response for the `ListCrawledUrls` method.", "id": "ListCrawledUrlsResponse", "properties": {"crawledUrls": {"description": "The list of CrawledUrls returned.", "items": {"$ref": "CrawledUrl"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}}, "type": "object"}, "ListFindingTypeStatsResponse": {"description": "Response for the `ListFindingTypeStats` method.", "id": "ListFindingTypeStatsResponse", "properties": {"findingTypeStats": {"description": "The list of FindingTypeStats returned.", "items": {"$ref": "FindingTypeStats"}, "type": "array"}}, "type": "object"}, "ListFindingsResponse": {"description": "Response for the `ListFindings` method.", "id": "ListFindingsResponse", "properties": {"findings": {"description": "The list of Findings returned.", "items": {"$ref": "Finding"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}}, "type": "object"}, "ListScanConfigsResponse": {"description": "Response for the `ListScanConfigs` method.", "id": "ListScanConfigsResponse", "properties": {"nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "scanConfigs": {"description": "The list of ScanConfigs returned.", "items": {"$ref": "ScanConfig"}, "type": "array"}}, "type": "object"}, "ListScanRunsResponse": {"description": "Response for the `ListScanRuns` method.", "id": "ListScanRunsResponse", "properties": {"nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "scanRuns": {"description": "The list of ScanRuns returned.", "items": {"$ref": "ScanRun"}, "type": "array"}}, "type": "object"}, "OutdatedLibrary": {"description": "Information reported for an outdated library.", "id": "OutdatedLibrary", "properties": {"learnMoreUrls": {"description": "URLs to learn more information about the vulnerabilities in the library.", "items": {"type": "string"}, "type": "array"}, "libraryName": {"description": "The name of the outdated library.", "type": "string"}, "version": {"description": "The version number.", "type": "string"}}, "type": "object"}, "ScanConfig": {"description": "A ScanConfig resource contains the configurations to launch a scan. next id: 12", "id": "ScanConfig", "properties": {"authentication": {"$ref": "Authentication", "description": "The authentication configuration. If specified, service will use the authentication configuration during scanning."}, "blacklistPatterns": {"description": "The excluded URL patterns as described in https://cloud.google.com/security-command-center/docs/how-to-use-web-security-scanner#excluding_urls", "items": {"type": "string"}, "type": "array"}, "displayName": {"description": "Required. The user provided display name of the ScanConfig.", "type": "string"}, "latestRun": {"$ref": "ScanRun", "description": "Latest ScanRun if available."}, "maxQps": {"description": "The maximum QPS during scanning. A valid value ranges from 5 to 20 inclusively. If the field is unspecified or its value is set 0, server will default to 15. Other values outside of [5, 20] range will be rejected with INVALID_ARGUMENT error.", "format": "int32", "type": "integer"}, "name": {"description": "The resource name of the ScanConfig. The name follows the format of 'projects/{projectId}/scanConfigs/{scanConfigId}'. The ScanConfig IDs are generated by the system.", "type": "string"}, "schedule": {"$ref": "Schedule", "description": "The schedule of the ScanConfig."}, "startingUrls": {"description": "Required. The starting URLs from which the scanner finds site pages.", "items": {"type": "string"}, "type": "array"}, "targetPlatforms": {"description": "Set of Google Cloud platforms targeted by the scan. If empty, APP_ENGINE will be used as a default.", "items": {"enum": ["TARGET_PLATFORM_UNSPECIFIED", "APP_ENGINE", "COMPUTE", "CLOUD_RUN", "CLOUD_FUNCTIONS"], "enumDescriptions": ["The target platform is unknown. Requests with this enum value will be rejected with INVALID_ARGUMENT error.", "Google App Engine service.", "Google Compute Engine service.", "Google Cloud Run service.", "Google Cloud Function service."], "type": "string"}, "type": "array"}, "userAgent": {"description": "The user agent used during scanning.", "enum": ["USER_AGENT_UNSPECIFIED", "CHROME_LINUX", "CHROME_ANDROID", "SAFARI_IPHONE"], "enumDescriptions": ["The user agent is unknown. Service will default to CHROME_LINUX.", "Chrome on Linux. This is the service default if unspecified.", "Chrome on Android.", "Safari on IPhone."], "type": "string"}}, "type": "object"}, "ScanRun": {"description": "A ScanRun is a output-only resource representing an actual run of the scan.", "id": "ScanRun", "properties": {"endTime": {"description": "The time at which the ScanRun reached termination state - that the ScanRun is either finished or stopped by user.", "format": "google-datetime", "type": "string"}, "executionState": {"description": "The execution state of the ScanRun.", "enum": ["EXECUTION_STATE_UNSPECIFIED", "QUEUED", "SCANNING", "FINISHED"], "enumDescriptions": ["Represents an invalid state caused by internal server error. This value should never be returned.", "The scan is waiting in the queue.", "The scan is in progress.", "The scan is either finished or stopped by user."], "type": "string"}, "hasVulnerabilities": {"description": "Whether the scan run has found any vulnerabilities.", "type": "boolean"}, "name": {"description": "The resource name of the ScanRun. The name follows the format of 'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'. The ScanRun IDs are generated by the system.", "type": "string"}, "progressPercent": {"description": "The percentage of total completion ranging from 0 to 100. If the scan is in queue, the value is 0. If the scan is running, the value ranges from 0 to 100. If the scan is finished, the value is 100.", "format": "int32", "type": "integer"}, "resultState": {"description": "The result state of the ScanRun. This field is only available after the execution state reaches \"FINISHED\".", "enum": ["RESULT_STATE_UNSPECIFIED", "SUCCESS", "ERROR", "KILLED"], "enumDescriptions": ["Default value. This value is returned when the ScanRun is not yet finished.", "The scan finished without errors.", "The scan finished with errors.", "The scan was terminated by user."], "type": "string"}, "startTime": {"description": "The time at which the ScanRun started.", "format": "google-datetime", "type": "string"}, "urlsCrawledCount": {"description": "The number of URLs crawled during this ScanRun. If the scan is in progress, the value represents the number of URLs crawled up to now.", "format": "int64", "type": "string"}, "urlsTestedCount": {"description": "The number of URLs tested during this ScanRun. If the scan is in progress, the value represents the number of URLs tested up to now. The number of URLs tested is usually larger than the number URLS crawled because typically a crawled URL is tested with multiple test payloads.", "format": "int64", "type": "string"}}, "type": "object"}, "Schedule": {"description": "Scan schedule configuration.", "id": "Schedule", "properties": {"intervalDurationDays": {"description": "Required. The duration of time between executions in days.", "format": "int32", "type": "integer"}, "scheduleTime": {"description": "A timestamp indicates when the next run will be scheduled. The value is refreshed by the server after each run. If unspecified, it will default to current server time, which means the scan will be scheduled to start immediately.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "StartScanRunRequest": {"description": "Request for the `StartScanRun` method.", "id": "StartScanRunRequest", "properties": {}, "type": "object"}, "StopScanRunRequest": {"description": "Request for the `StopScanRun` method.", "id": "StopScanRunRequest", "properties": {}, "type": "object"}, "ViolatingResource": {"description": "Information regarding any resource causing the vulnerability such as JavaScript sources, image, audio files, etc.", "id": "ViolatingResource", "properties": {"contentType": {"description": "The MIME type of this resource.", "type": "string"}, "resourceUrl": {"description": "URL of this violating resource.", "type": "string"}}, "type": "object"}, "VulnerableHeaders": {"description": "Information about vulnerable or missing HTTP Headers.", "id": "VulnerableHeaders", "properties": {"headers": {"description": "List of vulnerable headers.", "items": {"$ref": "Header"}, "type": "array"}, "missingHeaders": {"description": "List of missing headers.", "items": {"$ref": "Header"}, "type": "array"}}, "type": "object"}, "VulnerableParameters": {"description": "Information about vulnerable request parameters.", "id": "VulnerableParameters", "properties": {"parameterNames": {"description": "The vulnerable parameter names.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Xss": {"description": "Information reported for an XSS.", "id": "Xss", "properties": {"errorMessage": {"description": "An error message generated by a javascript breakage.", "type": "string"}, "stackTraces": {"description": "Stack traces leading to the point where the XSS occurred.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}}, "servicePath": "", "title": "Web Security Scanner API", "version": "v1alpha", "version_module": true}