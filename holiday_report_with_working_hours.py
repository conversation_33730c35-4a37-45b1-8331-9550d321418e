# Holiday Report with Working Hours - Python Implementation
# Based on the shift attendance report pattern

import frappe
from frappe import _
from frappe.utils import cint, flt


def execute(filters=None):
    columns = get_columns()
    data = get_data(filters)
    return columns, data


def get_columns():
    return [
        {
            "label": _("Employee ID"),
            "fieldname": "employee",
            "fieldtype": "Link",
            "options": "Employee",
            "width": 120,
        },
        {
            "label": _("Employee Name"),
            "fieldname": "employee_name",
            "fieldtype": "Data",
            "width": 150,
        },
        {
            "label": _("Department"),
            "fieldname": "department",
            "fieldtype": "Link",
            "options": "Department",
            "width": 120,
        },
        {
            "label": _("Designation"),
            "fieldname": "designation",
            "fieldtype": "Data",
            "width": 120,
        },
        {
            "label": _("Holiday Given (hrs)"),
            "fieldname": "holiday_given_hrs",
            "fieldtype": "Float",
            "width": 130,
            "precision": 2,
        },
        {
            "label": _("Holiday Taken (hrs)"),
            "fieldname": "holiday_taken_hrs",
            "fieldtype": "Float",
            "width": 130,
            "precision": 2,
        },
        {
            "label": _("Balance (hrs)"),
            "fieldname": "balance_hrs",
            "fieldtype": "Float",
            "width": 120,
            "precision": 2,
        },
        {
            "label": _("Average Working Hours"),
            "fieldname": "avg_working_hours",
            "fieldtype": "Float",
            "width": 140,
            "precision": 2,
        },
        {
            "label": _("Total Working Hours"),
            "fieldname": "total_working_hours",
            "fieldtype": "Float",
            "width": 140,
            "precision": 2,
        },
    ]


def get_data(filters):
    query = get_query(filters)
    data = query.run(as_dict=True)
    data = process_data(data, filters)
    return data


def get_query(filters):
    holiday = frappe.qb.DocType("Velocetec Holiday")
    employee = frappe.qb.DocType("Employee")
    attendance = frappe.qb.DocType("Attendance")

    # First get holiday data
    holiday_query = (
        frappe.qb.from_(holiday)
        .left_join(employee)
        .on(employee.name == holiday.employee)
        .select(
            holiday.employee,
            employee.employee_name,
            employee.department,
            employee.designation,
            holiday.type,
            holiday.hours,
            holiday.minutes,
            holiday.date,
        )
        .where(holiday.date.between(filters.get("from_date"), filters.get("to_date")))
    )

    # Apply additional filters if provided
    if filters.get("employee"):
        holiday_query = holiday_query.where(holiday.employee == filters.get("employee"))
    if filters.get("department"):
        holiday_query = holiday_query.where(employee.department == filters.get("department"))

    return holiday_query


def process_data(holiday_data, filters):
    # Group holiday data by employee
    employee_data = {}
    
    for record in holiday_data:
        employee_id = record.employee
        if employee_id not in employee_data:
            employee_data[employee_id] = {
                "employee": employee_id,
                "employee_name": record.employee_name,
                "department": record.department,
                "designation": record.designation,
                "holiday_given_hrs": 0,
                "holiday_taken_hrs": 0,
                "balance_hrs": 0,
                "avg_working_hours": 0,
                "total_working_hours": 0,
            }
        
        # Calculate holiday hours
        total_hours = flt(record.hours) + flt(record.minutes) / 60.0
        if record.type == "Given":
            employee_data[employee_id]["holiday_given_hrs"] += total_hours
        elif record.type == "Taken":
            employee_data[employee_id]["holiday_taken_hrs"] += total_hours

    # Get working hours from attendance for each employee
    for employee_id in employee_data.keys():
        working_hours_data = get_working_hours_for_employee(
            employee_id, filters.get("from_date"), filters.get("to_date")
        )
        employee_data[employee_id].update(working_hours_data)
        
        # Calculate balance
        employee_data[employee_id]["balance_hrs"] = (
            employee_data[employee_id]["holiday_given_hrs"] 
            - employee_data[employee_id]["holiday_taken_hrs"]
        )

    # Format the data for display
    result = []
    for emp_data in employee_data.values():
        # Round all float values to 2 decimal places
        for key in ["holiday_given_hrs", "holiday_taken_hrs", "balance_hrs", 
                   "avg_working_hours", "total_working_hours"]:
            emp_data[key] = flt(emp_data[key], 2)
        result.append(emp_data)

    return result


def get_working_hours_for_employee(employee, from_date, to_date):
    """Get working hours data for a specific employee within date range"""
    attendance = frappe.qb.DocType("Attendance")
    
    query = (
        frappe.qb.from_(attendance)
        .select(
            frappe.qb.functions.Avg(attendance.working_hours).as_("avg_working_hours"),
            frappe.qb.functions.Sum(attendance.working_hours).as_("total_working_hours"),
            frappe.qb.functions.Count(attendance.name).as_("attendance_count"),
        )
        .where(attendance.employee == employee)
        .where(attendance.attendance_date.between(from_date, to_date))
        .where(attendance.docstatus == 1)
        .where(attendance.status.isin(["Present", "Half Day", "Work From Home"]))
        .where(attendance.working_hours.isnotnull())
        .where(attendance.working_hours > 0)
    )
    
    result = query.run(as_dict=True)
    
    if result and result[0]:
        return {
            "avg_working_hours": flt(result[0].avg_working_hours or 0, 2),
            "total_working_hours": flt(result[0].total_working_hours or 0, 2),
        }
    else:
        return {
            "avg_working_hours": 0,
            "total_working_hours": 0,
        }


# Alternative function to get working hours for specific holiday dates
def get_working_hours_for_holiday_dates(employee, holiday_dates):
    """Get working hours for specific holiday dates"""
    if not holiday_dates:
        return 0
    
    attendance = frappe.qb.DocType("Attendance")
    
    query = (
        frappe.qb.from_(attendance)
        .select(frappe.qb.functions.Sum(attendance.working_hours))
        .where(attendance.employee == employee)
        .where(attendance.attendance_date.isin(holiday_dates))
        .where(attendance.docstatus == 1)
        .where(attendance.working_hours.isnotnull())
    )
    
    result = query.run()
    return flt(result[0][0] if result and result[0] and result[0][0] else 0, 2)
