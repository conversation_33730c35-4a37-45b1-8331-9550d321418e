set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250422_180002-work-database.sql.gz

Backup Summary for work at 2025-04-22 18:00:04.177432
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250422_180002-work-site_config_backup.json 258.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250422_180002-work-database.sql.gz 1.7MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250422_180002-work-database.sql.gz

Backup Summary for work at 2025-04-22 18:00:04.213031
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250422_180002-work-site_config_backup.json 258.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250422_180002-work-database.sql.gz 1.9MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250423_120002-work-database.sql.gz

Backup Summary for work at 2025-04-23 12:00:08.904630
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250423_120002-work-site_config_backup.json 258.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250423_120002-work-database.sql.gz 1.9MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250423_120002-work-database.sql.gz

Backup Summary for work at 2025-04-23 12:00:08.904907
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250423_120002-work-site_config_backup.json 258.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250423_120002-work-database.sql.gz 1.9MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250423_180002-work-database.sql.gz

Backup Summary for work at 2025-04-23 18:00:07.437832
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250423_180002-work-site_config_backup.json 258.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250423_180002-work-database.sql.gz 1.9MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250423_180002-work-database.sql.gz

Backup Summary for work at 2025-04-23 18:00:07.437837
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250423_180002-work-site_config_backup.json 258.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250423_180002-work-database.sql.gz 1.9MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7abf51a47240>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7abf50809fc0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7abf50809fc0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250423_120002-work-database.sql.gz', '20250422_180002-work-database.sql.gz', '20250422_180002-work-site_config_backup.json', '20250423_180002-work-database.sql.gz', '20250423_120002-work-site_config_backup.json', '20250423_180002-work-site_config_backup.json']
      this_file = '20250422_180002-work-database.sql.gz'
      this_file_path = './work/private/backups/20250422_180002-work-database.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250422_180002-work-database.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250424_120003-work-database.sql.gz

Backup Summary for work at 2025-04-24 12:00:07.873493
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250424_120003-work-site_config_backup.json 258.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250424_120003-work-database.sql.gz 974.8KiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7fcc726ab240>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7fcc714225c0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7fcc714225c0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250424_120003-work-site_config_backup.json', '20250423_120002-work-database.sql.gz', '20250423_180002-work-database.sql.gz', '20250424_120003-work-database.sql.gz', '20250423_120002-work-site_config_backup.json', '20250423_180002-work-site_config_backup.json']
      this_file = '20250423_120002-work-site_config_backup.json'
      this_file_path = './work/private/backups/20250423_120002-work-site_config_backup.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250423_120002-work-site_config_backup.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250424_180002-work-database.sql.gz

Backup Summary for work at 2025-04-24 18:00:05.197826
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250424_180002-work-site_config_backup.json 258.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250424_180002-work-database.sql.gz 974.8KiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250425_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-04-25 12:00:07.178186
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250425_120003-work-site_config_backup-enc.json 258.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250425_120003-work-database-enc.sql.gz 1.9MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250425_120003-work-database-enc.sql.gz

[Errno 2] No such file or directory: './work/private/backups/20250425_120003-work-database-enc.sql.gz.gpg' -> './work/private/backups/20250425_120003-work-database-enc.sql.gz'
Error occurred during encryption. Files are stored without encryption.
Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-04-25 12:00:07.424916
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250425_120003-work-site_config_backup-enc.json 258.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250425_120003-work-database-enc.sql.gz 1.9MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250425_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-04-25 18:00:05.721311
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250425_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250425_180003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250425_180003-work-database-enc.sql.gz

[Errno 2] No such file or directory: './work/private/backups/20250425_180003-work-database-enc.sql.gz.gpg' -> './work/private/backups/20250425_180003-work-database-enc.sql.gz'
Error occurred during encryption. Files are stored without encryption.
Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-04-25 18:00:05.723418
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250425_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250425_180003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7bbd3cbe16c0>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7bbd3bf29a50>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7bbd3bf29a50>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250425_180003-work-site_config_backup-enc.json', '20250425_180003-work-database-enc.sql.gz', '20250424_180002-work-database.sql.gz', '20250425_120003-work-site_config_backup-enc.json', '20250425_120003-work-database-enc.sql.gz', '20250424_180002-work-site_config_backup.json']
      this_file = '20250424_180002-work-database.sql.gz'
      this_file_path = './work/private/backups/20250424_180002-work-database.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250424_180002-work-database.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250426_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-04-26 12:00:07.877259
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250426_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250426_120003-work-database-enc.sql.gz 1009.7KiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250426_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-04-26 18:00:04.879573
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250426_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250426_180002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250426_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-04-26 18:00:04.965554
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250426_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250426_180002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250427_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-04-27 12:00:09.380796
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250427_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250427_120002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250427_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-04-27 12:00:09.438007
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250427_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250427_120002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250427_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-04-27 18:00:05.709007
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250427_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250427_180003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250427_180003-work-database-enc.sql.gz

[Errno 2] No such file or directory: './work/private/backups/20250427_180003-work-database-enc.sql.gz.gpg' -> './work/private/backups/20250427_180003-work-database-enc.sql.gz'
Error occurred during encryption. Files are stored without encryption.
Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-04-27 18:00:05.715151
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250427_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250427_180003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250428_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-04-28 12:00:08.720725
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250428_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250428_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250428_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-04-28 12:00:08.831771
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250428_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250428_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250428_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-04-28 18:00:05.562597
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250428_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250428_180002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250428_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-04-28 18:00:05.641240
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250428_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250428_180002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7495f07bd620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7495efb05c00>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7495efb05c00>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250428_120003-work-site_config_backup-enc.json', '20250427_180003-work-database-enc.sql.gz', '20250428_180002-work-database-enc.sql.gz', '20250428_120003-work-database-enc.sql.gz', '20250427_180003-work-site_config_backup-enc.json', '20250428_180002-work-site_config_backup-enc.json']
      this_file = '20250427_180003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250427_180003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250427_180003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250429_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-04-29 12:00:08.777993
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250429_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250429_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7be488cc9620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7be487a25e10>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7be487a25e10>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250428_120003-work-site_config_backup-enc.json', '20250429_120003-work-site_config_backup-enc.json', '20250428_180002-work-database-enc.sql.gz', '20250428_120003-work-database-enc.sql.gz', '20250429_120003-work-database-enc.sql.gz', '20250428_180002-work-site_config_backup-enc.json']
      this_file = '20250428_120003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250428_120003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250428_120003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250429_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-04-29 18:00:06.274964
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250429_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250429_180002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x754e64fad620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x754e64eeaad0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x754e64eeaad0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250429_180002-work-database-enc.sql.gz', '20250429_180002-work-site_config_backup-enc.json', '20250429_120003-work-site_config_backup-enc.json', '20250429_120003-work-database-enc.sql.gz', '20250428_180002-work-site_config_backup-enc.json']
      this_file = '20250428_180002-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250428_180002-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250428_180002-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250430_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-04-30 12:00:05.227025
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250430_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250430_120002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x703ca7ae1620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x703ca681ac50>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x703ca681ac50>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250430_120002-work-site_config_backup-enc.json', '20250429_180002-work-database-enc.sql.gz', '20250429_180002-work-site_config_backup-enc.json', '20250429_120003-work-site_config_backup-enc.json', '20250429_120003-work-database-enc.sql.gz', '20250430_120002-work-database-enc.sql.gz']
      this_file = '20250429_120003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250429_120003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250429_120003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250430_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-04-30 18:00:05.654837
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250430_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250430_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250501_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-01 12:00:12.184433
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250501_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250501_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250501_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-01 12:00:12.277062
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250501_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250501_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250501_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-01 18:00:05.170162
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250501_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250501_180002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250501_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-01 18:00:05.259765
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250501_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250501_180002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7f7612215620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f7611009c90>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f7611009c90>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250430_180003-work-database-enc.sql.gz', '20250501_120003-work-site_config_backup-enc.json', '20250430_180003-work-site_config_backup-enc.json', '20250501_180002-work-site_config_backup-enc.json', '20250501_120003-work-database-enc.sql.gz', '20250501_180002-work-database-enc.sql.gz']
      this_file = '20250430_180003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250430_180003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250430_180003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250502_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-02 12:00:10.085262
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250502_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250502_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7228d6ce5620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7228d5a1de40>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7228d5a1de40>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250501_120003-work-site_config_backup-enc.json', '20250502_120003-work-database-enc.sql.gz', '20250501_180002-work-site_config_backup-enc.json', '20250502_120003-work-site_config_backup-enc.json', '20250501_120003-work-database-enc.sql.gz', '20250501_180002-work-database-enc.sql.gz']
      this_file = '20250501_120003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250501_120003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250501_120003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250502_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-02 18:00:06.186558
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250502_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250502_180002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x71338adbd620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x71338a105a80>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x71338a105a80>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250502_120003-work-database-enc.sql.gz', '20250502_180002-work-site_config_backup-enc.json', '20250501_180002-work-site_config_backup-enc.json', '20250502_120003-work-site_config_backup-enc.json', '20250502_180002-work-database-enc.sql.gz', '20250501_180002-work-database-enc.sql.gz']
      this_file = '20250501_180002-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250501_180002-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250501_180002-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250503_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-03 12:00:11.696872
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250503_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250503_120002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250503_180002-work-database-enc.sql.gz

[Errno 2] No such file or directory: './work/private/backups/20250503_180002-work-database-enc.sql.gz.gpg' -> './work/private/backups/20250503_180002-work-database-enc.sql.gz'
Error occurred during encryption. Files are stored without encryption.
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250503_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-03 18:00:07.256820
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250503_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250503_180002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup Summary for work at 2025-05-03 18:00:07.256894
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250503_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250503_180002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x76f6749cd620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x76f673d1d720>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x76f673d1d720>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250503_120002-work-database-enc.sql.gz', '20250503_120002-work-site_config_backup-enc.json', '20250503_180002-work-database-enc.sql.gz', '20250503_180002-work-site_config_backup-enc.json', '20250502_180002-work-site_config_backup-enc.json', '20250502_180002-work-database-enc.sql.gz']
      this_file = '20250502_180002-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250502_180002-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250502_180002-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250504_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-04 12:00:09.648394
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250504_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250504_120002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x734f199c9620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x734f18741e40>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x734f18741e40>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250503_120002-work-database-enc.sql.gz', '20250503_120002-work-site_config_backup-enc.json', '20250503_180002-work-database-enc.sql.gz', '20250504_120002-work-database-enc.sql.gz', '20250503_180002-work-site_config_backup-enc.json', '20250504_120002-work-site_config_backup-enc.json']
      this_file = '20250503_120002-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250503_120002-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250503_120002-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250505_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-05 12:00:09.207992
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250505_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250505_120002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7903dfc9d620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7903dea25c00>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7903dea25c00>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250504_120002-work-database-enc.sql.gz', '20250504_120002-work-site_config_backup-enc.json', '20250505_120002-work-site_config_backup-enc.json', '20250505_120002-work-database-enc.sql.gz']
      this_file = '20250504_120002-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250504_120002-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250504_120002-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250505_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-05 18:00:05.870636
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250505_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250505_180002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250506_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250506_120003-work-database-enc.sql.gz

[Errno 2] No such file or directory: './work/private/backups/20250506_120003-work-database-enc.sql.gz.gpg' -> './work/private/backups/20250506_120003-work-database-enc.sql.gz'
Error occurred during encryption. Files are stored without encryption.
Backup Summary for work at 2025-05-06 12:00:12.309734
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250506_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250506_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-06 12:00:12.310005
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250506_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250506_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7befd9839620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7befd860d9f0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7befd860d9f0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250505_120002-work-site_config_backup-enc.json', '20250505_180002-work-site_config_backup-enc.json', '20250505_120002-work-database-enc.sql.gz', '20250505_180002-work-database-enc.sql.gz', '20250506_120003-work-site_config_backup-enc.json', '20250506_120003-work-database-enc.sql.gz']
      this_file = '20250505_120002-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250505_120002-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250505_120002-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250506_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-06 18:00:06.745709
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250506_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250506_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7f66815b1620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f66834066b0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f66834066b0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250506_180003-work-site_config_backup-enc.json', '20250505_180002-work-site_config_backup-enc.json', '20250505_180002-work-database-enc.sql.gz', '20250506_180003-work-database-enc.sql.gz', '20250506_120003-work-site_config_backup-enc.json', '20250506_120003-work-database-enc.sql.gz']
      this_file = '20250505_180002-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250505_180002-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250505_180002-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250507_000003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-07 00:00:12.437704
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250507_000003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250507_000003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250507_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-07 12:00:12.238597
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250507_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250507_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250507_120003-work-database-enc.sql.gz

[Errno 2] No such file or directory: './work/private/backups/20250507_120003-work-database-enc.sql.gz.gpg' -> './work/private/backups/20250507_120003-work-database-enc.sql.gz'
Error occurred during encryption. Files are stored without encryption.
Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-07 12:00:12.239445
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250507_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250507_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x777337abd620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7773368398a0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7773368398a0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250507_000003-work-site_config_backup-enc.json', '20250506_180003-work-site_config_backup-enc.json', '20250507_120003-work-database-enc.sql.gz', '20250507_120003-work-site_config_backup-enc.json', '20250507_000003-work-database-enc.sql.gz', '20250506_180003-work-database-enc.sql.gz', '20250506_120003-work-site_config_backup-enc.json', '20250506_120003-work-database-enc.sql.gz']
      this_file = '20250506_120003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250506_120003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250506_120003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250507_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-07 18:00:09.697103
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250507_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250507_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7d3865d75620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7d3865c79f60>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7d3865c79f60>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250507_180003-work-database-enc.sql.gz', '20250507_000003-work-site_config_backup-enc.json', '20250506_180003-work-site_config_backup-enc.json', '20250507_120003-work-database-enc.sql.gz', '20250507_120003-work-site_config_backup-enc.json', '20250507_000003-work-database-enc.sql.gz', '20250507_180003-work-site_config_backup-enc.json', '20250506_180003-work-database-enc.sql.gz']
      this_file = '20250506_180003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250506_180003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250506_180003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250508_000002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-08 00:00:08.188789
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250508_000002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250508_000002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x79f14a5f5620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x79f149935f30>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x79f149935f30>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250508_000002-work-database-enc.sql.gz', '20250507_180003-work-database-enc.sql.gz', '20250507_000003-work-site_config_backup-enc.json', '20250508_000002-work-site_config_backup-enc.json', '20250507_120003-work-database-enc.sql.gz', '20250507_120003-work-site_config_backup-enc.json', '20250507_000003-work-database-enc.sql.gz', '20250507_180003-work-site_config_backup-enc.json']
      this_file = '20250507_000003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250507_000003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250507_000003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250508_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-08 12:00:08.304778
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250508_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250508_120002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x71ec2b5b1620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x71ec2b4f99c0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x71ec2b4f99c0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250508_000002-work-database-enc.sql.gz', '20250507_180003-work-database-enc.sql.gz', '20250508_000002-work-site_config_backup-enc.json', '20250507_120003-work-database-enc.sql.gz', '20250508_120002-work-database-enc.sql.gz', '20250507_120003-work-site_config_backup-enc.json', '20250507_180003-work-site_config_backup-enc.json', '20250508_120002-work-site_config_backup-enc.json']
      this_file = '20250507_120003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250507_120003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250507_120003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250508_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-08 18:00:06.473469
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250508_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250508_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x70db32a11620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x70db31809d80>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x70db31809d80>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250508_000002-work-database-enc.sql.gz', '20250507_180003-work-database-enc.sql.gz', '20250508_180003-work-site_config_backup-enc.json', '20250508_000002-work-site_config_backup-enc.json', '20250508_180003-work-database-enc.sql.gz', '20250508_120002-work-database-enc.sql.gz', '20250507_180003-work-site_config_backup-enc.json', '20250508_120002-work-site_config_backup-enc.json']
      this_file = '20250508_000002-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250508_000002-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250508_000002-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250509_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-09 12:00:08.888602
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250509_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250509_120002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x720bfdf7d620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x720bfdf16c80>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x720bfdf16c80>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250508_180003-work-site_config_backup-enc.json', '20250509_120002-work-database-enc.sql.gz', '20250509_120002-work-site_config_backup-enc.json', '20250508_180003-work-database-enc.sql.gz', '20250508_120002-work-database-enc.sql.gz', '20250508_120002-work-site_config_backup-enc.json']
      this_file = '20250508_120002-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250508_120002-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250508_120002-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250509_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-09 18:00:05.484922
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250509_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250509_180002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250510_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250510_120002-work-database-enc.sql.gz

[Errno 2] No such file or directory: './work/private/backups/20250510_120002-work-database-enc.sql.gz.gpg' -> './work/private/backups/20250510_120002-work-database-enc.sql.gz'
Error occurred during encryption. Files are stored without encryption.
Backup Summary for work at 2025-05-10 12:00:11.965147
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250510_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250510_120002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-10 12:00:11.965419
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250510_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250510_120002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7d3f421ad620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7d3f428ee6b0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7d3f428ee6b0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250509_120002-work-database-enc.sql.gz', '20250509_120002-work-site_config_backup-enc.json', '20250510_120002-work-site_config_backup-enc.json', '20250510_120002-work-database-enc.sql.gz', '20250509_180002-work-site_config_backup-enc.json', '20250509_180002-work-database-enc.sql.gz']
      this_file = '20250509_120002-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250509_120002-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250509_120002-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250510_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-10 18:00:06.208854
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250510_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250510_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x70ccc57f5620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x70ccc4b35d80>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x70ccc4b35d80>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250510_180003-work-database-enc.sql.gz', '20250510_180003-work-site_config_backup-enc.json', '20250510_120002-work-site_config_backup-enc.json', '20250510_120002-work-database-enc.sql.gz', '20250509_180002-work-site_config_backup-enc.json', '20250509_180002-work-database-enc.sql.gz']
      this_file = '20250509_180002-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250509_180002-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250509_180002-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250511_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-11 12:00:11.512172
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250511_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250511_120002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250511_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-11 18:00:05.351135
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250511_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250511_180002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250511_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-11 18:00:05.358408
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250511_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250511_180002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7c8831fbd620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c8831e862c0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c8831e862c0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250510_180003-work-database-enc.sql.gz', '20250511_180002-work-database-enc.sql.gz', '20250511_120002-work-site_config_backup-enc.json', '20250510_180003-work-site_config_backup-enc.json', '20250511_120002-work-database-enc.sql.gz', '20250511_180002-work-site_config_backup-enc.json']
      this_file = '20250510_180003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250510_180003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250510_180003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250512_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-12 12:00:09.775816
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250512_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250512_120002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x732ffd7d1620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x732ffd7c66b0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x732ffd7c66b0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250511_180002-work-database-enc.sql.gz', '20250511_120002-work-site_config_backup-enc.json', '20250512_120002-work-site_config_backup-enc.json', '20250511_120002-work-database-enc.sql.gz', '20250511_180002-work-site_config_backup-enc.json', '20250512_120002-work-database-enc.sql.gz']
      this_file = '20250511_120002-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250511_120002-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250511_120002-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250512_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-12 18:00:06.955103
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250512_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250512_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7100e93bd620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7100e9356da0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7100e9356da0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250511_180002-work-database-enc.sql.gz', '20250512_120002-work-site_config_backup-enc.json', '20250512_180003-work-database-enc.sql.gz', '20250511_180002-work-site_config_backup-enc.json', '20250512_120002-work-database-enc.sql.gz', '20250512_180003-work-site_config_backup-enc.json']
      this_file = '20250511_180002-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250511_180002-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250511_180002-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250513_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-13 12:00:11.591290
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250513_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250513_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x79f5a23d9620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x79f5a1145e40>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x79f5a1145e40>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250512_120002-work-site_config_backup-enc.json', '20250512_180003-work-database-enc.sql.gz', '20250513_120003-work-site_config_backup-enc.json', '20250513_120003-work-database-enc.sql.gz', '20250512_120002-work-database-enc.sql.gz', '20250512_180003-work-site_config_backup-enc.json']
      this_file = '20250512_120002-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250512_120002-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250512_120002-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250513_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-13 18:00:06.012933
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250513_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250513_180002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7d05ec775620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7d05ecdb66b0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7d05ecdb66b0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250512_180003-work-database-enc.sql.gz', '20250513_120003-work-site_config_backup-enc.json', '20250513_180002-work-site_config_backup-enc.json', '20250513_120003-work-database-enc.sql.gz', '20250512_180003-work-site_config_backup-enc.json', '20250513_180002-work-database-enc.sql.gz']
      this_file = '20250512_180003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250512_180003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250512_180003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250514_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-14 12:00:10.579279
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250514_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250514_120002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250514_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-14 18:00:05.831306
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250514_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250514_180002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250514_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-14 18:00:05.934209
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250514_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250514_180002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250515_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-15 12:00:09.570609
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250515_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250515_120002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250515_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-15 12:00:09.679306
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250515_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250515_120002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7acf99dd9620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7acf9911de40>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7acf9911de40>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250514_120002-work-database-enc.sql.gz', '20250514_180002-work-site_config_backup-enc.json', '20250515_120002-work-site_config_backup-enc.json', '20250514_180002-work-database-enc.sql.gz', '20250515_120002-work-database-enc.sql.gz', '20250514_120002-work-site_config_backup-enc.json']
      this_file = '20250514_120002-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250514_120002-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250514_120002-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250515_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-15 18:00:06.241255
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250515_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250515_180002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250516_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-16 12:00:09.591410
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250516_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250516_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250516_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-16 12:00:09.664284
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250516_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250516_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x704f62b7d620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x704f62a81f60>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x704f62a81f60>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250516_120003-work-site_config_backup-enc.json', '20250515_180002-work-database-enc.sql.gz', '20250515_120002-work-site_config_backup-enc.json', '20250516_120003-work-database-enc.sql.gz', '20250515_120002-work-database-enc.sql.gz', '20250515_180002-work-site_config_backup-enc.json']
      this_file = '20250515_120002-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250515_120002-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250515_120002-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250516_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-16 18:00:09.036459
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250516_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250516_180002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7d5f965b9620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7d5f95901c60>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7d5f95901c60>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250516_180002-work-site_config_backup-enc.json', '20250516_180002-work-database-enc.sql.gz', '20250516_120003-work-site_config_backup-enc.json', '20250515_180002-work-database-enc.sql.gz', '20250516_120003-work-database-enc.sql.gz', '20250515_180002-work-site_config_backup-enc.json']
      this_file = '20250515_180002-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250515_180002-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250515_180002-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250517_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-17 12:00:13.387887
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250517_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250517_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x770c731e1620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x770c72525ed0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x770c72525ed0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250517_120003-work-site_config_backup-enc.json', '20250516_180002-work-site_config_backup-enc.json', '20250516_180002-work-database-enc.sql.gz', '20250516_120003-work-site_config_backup-enc.json', '20250516_120003-work-database-enc.sql.gz', '20250517_120003-work-database-enc.sql.gz']
      this_file = '20250516_120003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250516_120003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250516_120003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250517_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-17 18:00:06.972736
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250517_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250517_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x76072edbd620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x76072e101d80>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x76072e101d80>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250517_120003-work-site_config_backup-enc.json', '20250516_180002-work-site_config_backup-enc.json', '20250516_180002-work-database-enc.sql.gz', '20250517_120003-work-database-enc.sql.gz', '20250517_180003-work-site_config_backup-enc.json', '20250517_180003-work-database-enc.sql.gz']
      this_file = '20250517_120003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250517_120003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250517_120003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250518_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-18 18:00:12.631660
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250518_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250518_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x76024c1f5620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x76024b536230>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x76024b536230>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250518_180003-work-database-enc.sql.gz', '20250518_180003-work-site_config_backup-enc.json', '20250517_180003-work-site_config_backup-enc.json', '20250517_180003-work-database-enc.sql.gz']
      this_file = '20250517_180003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250517_180003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250517_180003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250519_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-19 12:00:09.284685
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250519_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250519_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250519_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-19 18:00:07.074713
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250519_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250519_180003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250519_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-19 18:00:07.184682
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250519_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250519_180003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x74c3441dd620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x74c343521f30>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x74c343521f30>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250519_120003-work-database-enc.sql.gz', '20250519_180003-work-database-enc.sql.gz', '20250519_180003-work-site_config_backup-enc.json', '20250519_120003-work-site_config_backup-enc.json', '20250518_180003-work-database-enc.sql.gz', '20250518_180003-work-site_config_backup-enc.json']
      this_file = '20250518_180003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250518_180003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250518_180003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250520_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-20 12:00:09.127030
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250520_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250520_120002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7b9847aad620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7b984841e6b0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7b984841e6b0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250519_120003-work-database-enc.sql.gz', '20250519_180003-work-database-enc.sql.gz', '20250519_180003-work-site_config_backup-enc.json', '20250519_120003-work-site_config_backup-enc.json', '20250520_120002-work-site_config_backup-enc.json', '20250520_120002-work-database-enc.sql.gz']
      this_file = '20250519_120003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250519_120003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250519_120003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250520_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-20 18:00:05.955654
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250520_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250520_180002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x76c1b0dc5620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x76c1b0109c30>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x76c1b0109c30>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250520_180002-work-database-enc.sql.gz', '20250519_180003-work-database-enc.sql.gz', '20250519_180003-work-site_config_backup-enc.json', '20250520_180002-work-site_config_backup-enc.json', '20250520_120002-work-site_config_backup-enc.json', '20250520_120002-work-database-enc.sql.gz']
      this_file = '20250519_180003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250519_180003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250519_180003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250521_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-21 12:00:11.112700
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250521_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250521_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x72555d29d620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x72555c039db0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x72555c039db0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250520_180002-work-database-enc.sql.gz', '20250521_120003-work-site_config_backup-enc.json', '20250520_180002-work-site_config_backup-enc.json', '20250521_120003-work-database-enc.sql.gz', '20250520_120002-work-site_config_backup-enc.json', '20250520_120002-work-database-enc.sql.gz']
      this_file = '20250520_120002-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250520_120002-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250520_120002-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250521_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-21 18:00:10.029151
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250521_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250521_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7c62c11dd620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c62c20de6b0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c62c20de6b0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250521_180003-work-database-enc.sql.gz', '20250520_180002-work-database-enc.sql.gz', '20250521_120003-work-site_config_backup-enc.json', '20250520_180002-work-site_config_backup-enc.json', '20250521_120003-work-database-enc.sql.gz', '20250521_180003-work-site_config_backup-enc.json']
      this_file = '20250520_180002-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250520_180002-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250520_180002-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250522_000003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-22 00:00:09.781496
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250522_000003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250522_000003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250522_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-22 12:00:10.265670
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250522_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250522_120002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250522_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-22 12:00:10.397239
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250522_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250522_120002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7a89dbf7d620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7a89dbe859c0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7a89dbe859c0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250522_120002-work-site_config_backup-enc.json', '20250521_180003-work-database-enc.sql.gz', '20250521_120003-work-site_config_backup-enc.json', '20250521_120003-work-database-enc.sql.gz', '20250522_120002-work-database-enc.sql.gz', '20250522_000003-work-database-enc.sql.gz', '20250522_000003-work-site_config_backup-enc.json', '20250521_180003-work-site_config_backup-enc.json']
      this_file = '20250521_120003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250521_120003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250521_120003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250523_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-23 12:00:09.695716
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250523_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250523_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7612819b9620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x761280d05900>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x761280d05900>
  File "apps/frappe/frappe/utils/backups.py", line 661, in delete_temp_backups
    if is_file_old(this_file_path, older_than):
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250522_120002-work-site_config_backup-enc.json', '20250523_120003-work-database-enc.sql.gz', '20250523_120003-work-site_config_backup-enc.json', '20250522_120002-work-database-enc.sql.gz']
      this_file = '20250522_120002-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250522_120002-work-site_config_backup-enc.json'
  File "apps/frappe/frappe/utils/backups.py", line 671, in is_file_old
    file_datetime = datetime.fromtimestamp(os.stat(file_path).st_ctime)
      file_path = './work/private/backups/20250522_120002-work-site_config_backup-enc.json'
      older_than = 24
      timedelta = <class 'datetime.timedelta'>
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250522_120002-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250523_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-23 18:00:05.879779
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250523_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250523_180002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250524_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-24 12:00:10.516420
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250524_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250524_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250524_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-24 12:00:10.650613
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250524_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250524_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7c934aa19620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c934980a110>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c934980a110>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250523_180002-work-database-enc.sql.gz', '20250523_120003-work-database-enc.sql.gz', '20250524_120003-work-site_config_backup-enc.json', '20250523_180002-work-site_config_backup-enc.json', '20250523_120003-work-site_config_backup-enc.json', '20250524_120003-work-database-enc.sql.gz']
      this_file = '20250523_120003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250523_120003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250523_120003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250524_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-24 18:00:05.769766
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250524_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250524_180002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x786133bc9620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x786132f11bd0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x786132f11bd0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250524_180002-work-database-enc.sql.gz', '20250523_180002-work-database-enc.sql.gz', '20250524_120003-work-site_config_backup-enc.json', '20250524_180002-work-site_config_backup-enc.json', '20250523_180002-work-site_config_backup-enc.json', '20250524_120003-work-database-enc.sql.gz']
      this_file = '20250523_180002-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250523_180002-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250523_180002-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250525_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-25 12:00:11.461380
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250525_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250525_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250525_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-25 18:00:06.683814
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250525_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250525_180003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250525_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-25 18:00:06.764824
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250525_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250525_180003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250526_120003-work-database-enc.sql.gz

[Errno 2] No such file or directory: './work/private/backups/20250526_120003-work-database-enc.sql.gz.gpg' -> './work/private/backups/20250526_120003-work-database-enc.sql.gz'
Error occurred during encryption. Files are stored without encryption.
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250526_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-26 12:00:10.689058
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250526_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250526_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-26 12:00:10.689315
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250526_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250526_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7ecc063b1620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7ecc062f9600>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7ecc062f9600>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250526_120003-work-site_config_backup-enc.json', '20250525_120003-work-database-enc.sql.gz', '20250525_180003-work-database-enc.sql.gz', '20250526_120003-work-database-enc.sql.gz', '20250525_180003-work-site_config_backup-enc.json', '20250525_120003-work-site_config_backup-enc.json']
      this_file = '20250525_120003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250525_120003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250525_120003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250526_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-26 18:00:08.529720
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250526_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250526_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7ba6dc6d1620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7ba6dc6c66b0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7ba6dc6c66b0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250526_120003-work-site_config_backup-enc.json', '20250526_180003-work-database-enc.sql.gz', '20250526_180003-work-site_config_backup-enc.json', '20250525_180003-work-database-enc.sql.gz', '20250526_120003-work-database-enc.sql.gz', '20250525_180003-work-site_config_backup-enc.json']
      this_file = '20250525_180003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250525_180003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250525_180003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250527_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-27 12:00:10.520194
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250527_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250527_120002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x78691a2e5620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x78691a3f66b0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x78691a3f66b0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250526_120003-work-site_config_backup-enc.json', '20250526_180003-work-database-enc.sql.gz', '20250526_180003-work-site_config_backup-enc.json', '20250526_120003-work-database-enc.sql.gz', '20250527_120002-work-site_config_backup-enc.json', '20250527_120002-work-database-enc.sql.gz']
      this_file = '20250526_120003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250526_120003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250526_120003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250527_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-27 18:00:06.969178
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250527_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250527_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7c45598d5620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c455a70e6b0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c455a70e6b0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250526_180003-work-database-enc.sql.gz', '20250526_180003-work-site_config_backup-enc.json', '20250527_180003-work-site_config_backup-enc.json', '20250527_120002-work-site_config_backup-enc.json', '20250527_120002-work-database-enc.sql.gz', '20250527_180003-work-database-enc.sql.gz']
      this_file = '20250526_180003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250526_180003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250526_180003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250528_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-28 12:00:12.293978
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250528_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250528_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7a2cfb5fd620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7a2cfb9feda0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7a2cfb9feda0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250528_120003-work-site_config_backup-enc.json', '20250528_120003-work-database-enc.sql.gz', '20250527_180003-work-site_config_backup-enc.json', '20250527_120002-work-site_config_backup-enc.json', '20250527_120002-work-database-enc.sql.gz', '20250527_180003-work-database-enc.sql.gz']
      this_file = '20250527_120002-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250527_120002-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250527_120002-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250528_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-28 18:00:09.631903
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250528_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250528_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7b8a996d5620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7b8a98405f00>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7b8a98405f00>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250528_120003-work-site_config_backup-enc.json', '20250528_120003-work-database-enc.sql.gz', '20250528_180003-work-database-enc.sql.gz', '20250527_180003-work-site_config_backup-enc.json', '20250528_180003-work-site_config_backup-enc.json', '20250527_180003-work-database-enc.sql.gz']
      this_file = '20250527_180003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250527_180003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250527_180003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250529_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-29 12:00:10.460103
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250529_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250529_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x723ca1a19620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x723ca0809f00>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x723ca0809f00>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250528_120003-work-site_config_backup-enc.json', '20250528_120003-work-database-enc.sql.gz', '20250528_180003-work-database-enc.sql.gz', '20250528_180003-work-site_config_backup-enc.json', '20250529_120003-work-site_config_backup-enc.json', '20250529_120003-work-database-enc.sql.gz']
      this_file = '20250528_120003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250528_120003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250528_120003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250529_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-29 18:00:06.075753
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250529_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250529_180002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x75346bc31620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x75346aa0df90>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x75346aa0df90>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250528_180003-work-database-enc.sql.gz', '20250529_180002-work-database-enc.sql.gz', '20250529_180002-work-site_config_backup-enc.json', '20250528_180003-work-site_config_backup-enc.json', '20250529_120003-work-site_config_backup-enc.json', '20250529_120003-work-database-enc.sql.gz']
      this_file = '20250528_180003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250528_180003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250528_180003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250530_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-30 12:00:11.305968
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250530_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250530_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7f1d3c839620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f1d3ca0e770>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f1d3ca0e770>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250530_120003-work-site_config_backup-enc.json', '20250529_180002-work-database-enc.sql.gz', '20250529_180002-work-site_config_backup-enc.json', '20250529_120003-work-site_config_backup-enc.json', '20250529_120003-work-database-enc.sql.gz', '20250530_120003-work-database-enc.sql.gz']
      this_file = '20250529_120003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250529_120003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250529_120003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250530_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-30 18:00:06.452586
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250530_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250530_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7710e23f9620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7710e134eda0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7710e134eda0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250530_180003-work-database-enc.sql.gz', '20250530_120003-work-site_config_backup-enc.json', '20250529_180002-work-database-enc.sql.gz', '20250529_180002-work-site_config_backup-enc.json', '20250530_180003-work-site_config_backup-enc.json', '20250530_120003-work-database-enc.sql.gz']
      this_file = '20250529_180002-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250529_180002-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250529_180002-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250531_000002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-31 00:00:09.530346
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250531_000002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250531_000002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x780fc6805620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x780fc5611b40>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x780fc5611b40>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250530_180003-work-database-enc.sql.gz', '20250531_000002-work-database-enc.sql.gz', '20250530_120003-work-site_config_backup-enc.json', '20250531_000002-work-site_config_backup-enc.json', '20250530_180003-work-site_config_backup-enc.json', '20250530_120003-work-database-enc.sql.gz']
      this_file = '20250530_120003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250530_120003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250530_120003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250531_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-05-31 18:00:10.964878
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250531_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250531_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x707fd8bbd620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x707fd7f097b0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x707fd7f097b0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250531_180003-work-database-enc.sql.gz', '20250530_180003-work-database-enc.sql.gz', '20250531_000002-work-database-enc.sql.gz', '20250531_000002-work-site_config_backup-enc.json', '20250531_180003-work-site_config_backup-enc.json', '20250530_180003-work-site_config_backup-enc.json']
      this_file = '20250530_180003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250530_180003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250530_180003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250601_000003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-01 00:00:11.276075
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250601_000003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250601_000003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7c531f1d5620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c531e519bd0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c531e519bd0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250531_180003-work-database-enc.sql.gz', '20250531_000002-work-database-enc.sql.gz', '20250531_000002-work-site_config_backup-enc.json', '20250531_180003-work-site_config_backup-enc.json', '20250601_000003-work-database-enc.sql.gz', '20250601_000003-work-site_config_backup-enc.json']
      this_file = '20250531_000002-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250531_000002-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250531_000002-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250601_180005-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-01 18:00:14.222762
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250601_180005-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250601_180005-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7a960fdd5620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7a960f1162c0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7a960f1162c0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250531_180003-work-database-enc.sql.gz', '20250531_180003-work-site_config_backup-enc.json', '20250601_180005-work-site_config_backup-enc.json', '20250601_000003-work-database-enc.sql.gz', '20250601_000003-work-site_config_backup-enc.json', '20250601_180005-work-database-enc.sql.gz']
      this_file = '20250531_180003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250531_180003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250531_180003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250602_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-02 12:00:11.233869
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250602_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250602_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250602_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-02 18:00:10.166358
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250602_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250602_180003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250602_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-02 18:00:10.353941
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250602_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250602_180003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250603_000002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-03 00:00:09.291552
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250603_000002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250603_000002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250603_000002-work-database-enc.sql.gz

[Errno 2] No such file or directory: './work/private/backups/20250603_000002-work-database-enc.sql.gz.gpg' -> './work/private/backups/20250603_000002-work-database-enc.sql.gz'
Error occurred during encryption. Files are stored without encryption.
Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-03 00:00:09.293296
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250603_000002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250603_000002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250603_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-03 12:00:09.270787
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250603_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250603_120002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250603_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-03 12:00:09.357052
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250603_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250603_120002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x70a8145ed620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x70a813935c00>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x70a813935c00>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250602_120003-work-site_config_backup-enc.json', '20250602_180003-work-site_config_backup-enc.json', '20250603_120002-work-database-enc.sql.gz', '20250602_180003-work-database-enc.sql.gz', '20250602_120003-work-database-enc.sql.gz', '20250603_000002-work-site_config_backup-enc.json', '20250603_120002-work-site_config_backup-enc.json', '20250603_000002-work-database-enc.sql.gz']
      this_file = '20250602_120003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250602_120003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250602_120003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250603_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-03 18:00:07.197997
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250603_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250603_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x75bc29be1620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x75bc2a2c66b0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x75bc2a2c66b0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250603_180003-work-site_config_backup-enc.json', '20250602_180003-work-site_config_backup-enc.json', '20250603_120002-work-database-enc.sql.gz', '20250603_180003-work-database-enc.sql.gz', '20250602_180003-work-database-enc.sql.gz', '20250603_000002-work-site_config_backup-enc.json', '20250603_120002-work-site_config_backup-enc.json', '20250603_000002-work-database-enc.sql.gz']
      this_file = '20250602_180003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250602_180003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250602_180003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250604_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-04 12:00:11.026784
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250604_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250604_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7943b5ee5620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7943b4c21e10>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7943b4c21e10>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250603_180003-work-site_config_backup-enc.json', '20250604_120003-work-database-enc.sql.gz', '20250603_120002-work-database-enc.sql.gz', '20250603_180003-work-database-enc.sql.gz', '20250603_120002-work-site_config_backup-enc.json', '20250604_120003-work-site_config_backup-enc.json']
      this_file = '20250603_120002-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250603_120002-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250603_120002-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250604_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-04 18:00:07.344876
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250604_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250604_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x78a719fed620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x78a718e09d50>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x78a718e09d50>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250603_180003-work-site_config_backup-enc.json', '20250604_180003-work-database-enc.sql.gz', '20250604_120003-work-database-enc.sql.gz', '20250604_180003-work-site_config_backup-enc.json', '20250603_180003-work-database-enc.sql.gz', '20250604_120003-work-site_config_backup-enc.json']
      this_file = '20250603_180003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250603_180003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250603_180003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250605_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-05 12:00:09.733314
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250605_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250605_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7c76e17f9620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c76e0605b10>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c76e0605b10>
  File "apps/frappe/frappe/utils/backups.py", line 661, in delete_temp_backups
    if is_file_old(this_file_path, older_than):
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250604_180003-work-database-enc.sql.gz', '20250604_120003-work-database-enc.sql.gz', '20250604_180003-work-site_config_backup-enc.json', '20250605_120003-work-database-enc.sql.gz', '20250605_120003-work-site_config_backup-enc.json', '20250604_120003-work-site_config_backup-enc.json']
      this_file = '20250604_120003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250604_120003-work-database-enc.sql.gz'
  File "apps/frappe/frappe/utils/backups.py", line 671, in is_file_old
    file_datetime = datetime.fromtimestamp(os.stat(file_path).st_ctime)
      file_path = './work/private/backups/20250604_120003-work-database-enc.sql.gz'
      older_than = 24
      timedelta = <class 'datetime.timedelta'>
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250604_120003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250605_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-05 18:00:06.433170
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250605_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250605_180002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7a4a94839620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7a4a937bece0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7a4a937bece0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250604_180003-work-database-enc.sql.gz', '20250605_180002-work-site_config_backup-enc.json', '20250604_180003-work-site_config_backup-enc.json', '20250605_120003-work-database-enc.sql.gz', '20250605_120003-work-site_config_backup-enc.json', '20250605_180002-work-database-enc.sql.gz']
      this_file = '20250604_180003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250604_180003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250604_180003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250606_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-06 12:00:10.040251
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250606_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250606_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7491d08ad620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7491cf631de0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7491cf631de0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250605_180002-work-site_config_backup-enc.json', '20250606_120003-work-site_config_backup-enc.json', '20250606_120003-work-database-enc.sql.gz', '20250605_120003-work-database-enc.sql.gz', '20250605_120003-work-site_config_backup-enc.json', '20250605_180002-work-database-enc.sql.gz']
      this_file = '20250605_120003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250605_120003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250605_120003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250606_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-06 18:00:06.376205
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250606_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250606_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x736ad3c95620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x736ad2a39b70>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x736ad2a39b70>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250606_180003-work-site_config_backup-enc.json', '20250605_180002-work-site_config_backup-enc.json', '20250606_180003-work-database-enc.sql.gz', '20250606_120003-work-site_config_backup-enc.json', '20250606_120003-work-database-enc.sql.gz', '20250605_180002-work-database-enc.sql.gz']
      this_file = '20250606_180003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250606_180003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250606_180003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250608_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-08 12:00:10.205273
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250608_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250608_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250608_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-08 18:00:05.775926
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250608_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250608_180002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250608_180002-work-database-enc.sql.gz

[Errno 2] No such file or directory: './work/private/backups/20250608_180002-work-database-enc.sql.gz.gpg' -> './work/private/backups/20250608_180002-work-database-enc.sql.gz'
Error occurred during encryption. Files are stored without encryption.
Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-08 18:00:05.780792
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250608_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250608_180002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250609_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-09 12:00:10.001589
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250609_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250609_120002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250609_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-09 12:00:10.138109
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250609_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250609_120002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x75848d7e1620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x75848f6066b0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x75848f6066b0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250608_120003-work-site_config_backup-enc.json', '20250609_120002-work-site_config_backup-enc.json', '20250609_120002-work-database-enc.sql.gz', '20250608_120003-work-database-enc.sql.gz', '20250608_180002-work-database-enc.sql.gz', '20250608_180002-work-site_config_backup-enc.json']
      this_file = '20250608_120003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250608_120003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250608_120003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250609_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-09 18:00:06.268102
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250609_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250609_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250610_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-10 12:00:09.933027
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250610_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250610_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250610_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-10 12:00:10.009343
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250610_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250610_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x73c08d1c1620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x73c08c5020e0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x73c08c5020e0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250609_180003-work-database-enc.sql.gz', '20250609_180003-work-site_config_backup-enc.json', '20250610_120003-work-database-enc.sql.gz', '20250609_120002-work-site_config_backup-enc.json', '20250609_120002-work-database-enc.sql.gz', '20250610_120003-work-site_config_backup-enc.json']
      this_file = '20250609_120002-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250609_120002-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250609_120002-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250610_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-10 18:00:07.170504
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250610_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250610_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x724a56275620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x724a55031bd0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x724a55031bd0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250609_180003-work-database-enc.sql.gz', '20250609_180003-work-site_config_backup-enc.json', '20250610_180003-work-database-enc.sql.gz', '20250610_120003-work-database-enc.sql.gz', '20250610_180003-work-site_config_backup-enc.json', '20250610_120003-work-site_config_backup-enc.json']
      this_file = '20250609_180003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250609_180003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250609_180003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250611_000003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-11 00:00:10.907043
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250611_000003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250611_000003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250611_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-11 12:00:10.663651
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250611_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250611_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250611_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-11 12:00:10.754953
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250611_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250611_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7ed7ca0b1620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7ed7c8e078e0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7ed7c8e078e0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250610_180003-work-database-enc.sql.gz', '20250611_120003-work-database-enc.sql.gz', '20250610_180003-work-site_config_backup-enc.json', '20250611_120003-work-site_config_backup-enc.json', '20250611_000003-work-database-enc.sql.gz', '20250610_120003-work-site_config_backup-enc.json', '20250611_000003-work-site_config_backup-enc.json']
      this_file = '20250610_120003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250610_120003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250610_120003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250611_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-11 18:00:10.127768
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250611_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250611_180002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x70be753f5620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x70be74215c60>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x70be74215c60>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250610_180003-work-database-enc.sql.gz', '20250611_120003-work-database-enc.sql.gz', '20250610_180003-work-site_config_backup-enc.json', '20250611_180002-work-database-enc.sql.gz', '20250611_120003-work-site_config_backup-enc.json', '20250611_000003-work-database-enc.sql.gz', '20250611_000003-work-site_config_backup-enc.json', '20250611_180002-work-site_config_backup-enc.json']
      this_file = '20250610_180003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250610_180003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250610_180003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250612_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-12 12:00:09.289268
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250612_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250612_120002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7722f7ae5620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7722f851e6b0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7722f851e6b0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250612_120002-work-site_config_backup-enc.json', '20250611_120003-work-database-enc.sql.gz', '20250611_180002-work-database-enc.sql.gz', '20250611_120003-work-site_config_backup-enc.json', '20250612_120002-work-database-enc.sql.gz', '20250611_180002-work-site_config_backup-enc.json']
      this_file = '20250611_120003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250611_120003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250611_120003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250612_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-12 18:00:05.298676
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250612_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250612_180002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x721e1a699620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x721e19429a50>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x721e19429a50>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250612_120002-work-site_config_backup-enc.json', '20250612_120002-work-database-enc.sql.gz', '20250612_180002-work-database-enc.sql.gz', '20250612_180002-work-site_config_backup-enc.json', '20250611_180002-work-site_config_backup-enc.json']
      this_file = '20250611_180002-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250611_180002-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250611_180002-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250613_000003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-13 00:00:10.519158
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250613_000003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250613_000003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250613_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250613_120003-work-database-enc.sql.gz

[Errno 2] No such file or directory: './work/private/backups/20250613_120003-work-database-enc.sql.gz.gpg' -> './work/private/backups/20250613_120003-work-database-enc.sql.gz'
Error occurred during encryption. Files are stored without encryption.
Backup Summary for work at 2025-06-13 12:00:10.121865
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250613_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250613_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-13 12:00:10.122120
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250613_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250613_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7c7c49fb1620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c7c49ef5d20>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c7c49ef5d20>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250612_120002-work-site_config_backup-enc.json', '20250613_120003-work-database-enc.sql.gz', '20250613_000003-work-database-enc.sql.gz', '20250612_120002-work-database-enc.sql.gz', '20250612_180002-work-database-enc.sql.gz', '20250612_180002-work-site_config_backup-enc.json', '20250613_000003-work-site_config_backup-enc.json', '20250613_120003-work-site_config_backup-enc.json']
      this_file = '20250612_120002-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250612_120002-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250612_120002-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250613_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-13 18:00:08.609758
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250613_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250613_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x726ac7cad620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x726ac6a21fc0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x726ac6a21fc0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250613_180003-work-database-enc.sql.gz', '20250613_120003-work-database-enc.sql.gz', '20250613_180003-work-site_config_backup-enc.json', '20250613_000003-work-database-enc.sql.gz', '20250612_180002-work-database-enc.sql.gz', '20250612_180002-work-site_config_backup-enc.json', '20250613_000003-work-site_config_backup-enc.json', '20250613_120003-work-site_config_backup-enc.json']
      this_file = '20250613_120003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250613_120003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250613_120003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250614_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-14 18:00:10.015203
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250614_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250614_180002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x758674095620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7586757e66b0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7586757e66b0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250614_180002-work-database-enc.sql.gz', '20250614_180002-work-site_config_backup-enc.json', '20250613_180003-work-database-enc.sql.gz', '20250613_180003-work-site_config_backup-enc.json']
      this_file = '20250613_180003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250613_180003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250613_180003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250615_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-15 12:00:10.233912
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250615_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250615_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250615_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-15 18:00:05.636824
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250615_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250615_180002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250615_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-15 18:00:05.712483
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250615_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250615_180002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x71c2b5bf9620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x71c2b4a19ae0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x71c2b4a19ae0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250614_180002-work-database-enc.sql.gz', '20250615_180002-work-database-enc.sql.gz', '20250614_180002-work-site_config_backup-enc.json', '20250615_180002-work-site_config_backup-enc.json', '20250615_120003-work-site_config_backup-enc.json', '20250615_120003-work-database-enc.sql.gz']
      this_file = '20250614_180002-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250614_180002-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250614_180002-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250616_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-16 12:00:09.984321
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250616_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250616_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7f28c8c95620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f28c7a35d50>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f28c7a35d50>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250615_180002-work-database-enc.sql.gz', '20250615_180002-work-site_config_backup-enc.json', '20250616_120003-work-site_config_backup-enc.json', '20250616_120003-work-database-enc.sql.gz', '20250615_120003-work-site_config_backup-enc.json', '20250615_120003-work-database-enc.sql.gz']
      this_file = '20250615_120003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250615_120003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250615_120003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250616_180005-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-16 18:00:13.448308
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250616_180005-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250616_180005-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x707c09be5620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x707c0aa9e6b0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x707c0aa9e6b0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250615_180002-work-database-enc.sql.gz', '20250616_180005-work-site_config_backup-enc.json', '20250615_180002-work-site_config_backup-enc.json', '20250616_120003-work-site_config_backup-enc.json', '20250616_120003-work-database-enc.sql.gz', '20250616_180005-work-database-enc.sql.gz']
      this_file = '20250615_180002-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250615_180002-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250615_180002-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250617_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-17 12:00:11.846641
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250617_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250617_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7118b0abd620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7118af831fc0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7118af831fc0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250617_120003-work-site_config_backup-enc.json', '20250617_120003-work-database-enc.sql.gz', '20250616_180005-work-site_config_backup-enc.json', '20250616_120003-work-site_config_backup-enc.json', '20250616_120003-work-database-enc.sql.gz', '20250616_180005-work-database-enc.sql.gz']
      this_file = '20250616_120003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250616_120003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250616_120003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250617_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-17 18:00:07.447301
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250617_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250617_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7edc4d9c9620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7edc4cd11c30>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7edc4cd11c30>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250617_120003-work-site_config_backup-enc.json', '20250617_180003-work-database-enc.sql.gz', '20250617_120003-work-database-enc.sql.gz', '20250616_180005-work-site_config_backup-enc.json', '20250617_180003-work-site_config_backup-enc.json', '20250616_180005-work-database-enc.sql.gz']
      this_file = '20250616_180005-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250616_180005-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250616_180005-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250618_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-18 12:00:11.585541
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250618_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250618_120002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7a6ed1b81620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7a6ed388e6b0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7a6ed388e6b0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250617_120003-work-site_config_backup-enc.json', '20250617_180003-work-database-enc.sql.gz', '20250617_120003-work-database-enc.sql.gz', '20250617_180003-work-site_config_backup-enc.json', '20250618_120002-work-database-enc.sql.gz', '20250618_120002-work-site_config_backup-enc.json']
      this_file = '20250617_120003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250617_120003-work-site_config_backup-enc.json'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250617_120003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250618_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-18 18:00:06.308543
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250618_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250618_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x777efecad620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x777efda21fc0>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x777efda21fc0>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250617_180003-work-database-enc.sql.gz', '20250618_180003-work-site_config_backup-enc.json', '20250617_180003-work-site_config_backup-enc.json', '20250618_120002-work-database-enc.sql.gz', '20250618_180003-work-database-enc.sql.gz', '20250618_120002-work-site_config_backup-enc.json']
      this_file = '20250617_180003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250617_180003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250617_180003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250619_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-19 12:00:10.267031
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250619_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250619_120002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x78fed15c5620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x78fed0905f00>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x78fed0905f00>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250619_120002-work-database-enc.sql.gz', '20250618_180003-work-site_config_backup-enc.json', '20250619_120002-work-site_config_backup-enc.json', '20250618_120002-work-database-enc.sql.gz', '20250618_180003-work-database-enc.sql.gz', '20250618_120002-work-site_config_backup-enc.json']
      this_file = '20250618_120002-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250618_120002-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250618_120002-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250619_180002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-19 18:00:09.553886
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250619_180002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250619_180002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x75f9755bd620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x75f974905c90>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x75f974905c90>
  File "apps/frappe/frappe/utils/backups.py", line 661, in delete_temp_backups
    if is_file_old(this_file_path, older_than):
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250619_180002-work-database-enc.sql.gz', '20250619_120002-work-database-enc.sql.gz', '20250618_180003-work-site_config_backup-enc.json', '20250619_180002-work-site_config_backup-enc.json', '20250619_120002-work-site_config_backup-enc.json', '20250618_180003-work-database-enc.sql.gz']
      this_file = '20250618_180003-work-site_config_backup-enc.json'
      this_file_path = './work/private/backups/20250618_180003-work-site_config_backup-enc.json'
  File "apps/frappe/frappe/utils/backups.py", line 671, in is_file_old
    file_datetime = datetime.fromtimestamp(os.stat(file_path).st_ctime)
      file_path = './work/private/backups/20250618_180003-work-site_config_backup-enc.json'
      older_than = 24
      timedelta = <class 'datetime.timedelta'>
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250618_180003-work-site_config_backup-enc.json'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250620_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-20 12:00:09.870112
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250620_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250620_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7b80833c1620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7b8082705f60>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7b8082705f60>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250619_180002-work-database-enc.sql.gz', '20250620_120003-work-site_config_backup-enc.json', '20250619_120002-work-database-enc.sql.gz', '20250619_180002-work-site_config_backup-enc.json', '20250620_120003-work-database-enc.sql.gz', '20250619_120002-work-site_config_backup-enc.json']
      this_file = '20250619_120002-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250619_120002-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250619_120002-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250620_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-20 18:00:12.742488
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250620_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250620_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7c82445bd620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c8243901f00>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7c8243901f00>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250619_180002-work-database-enc.sql.gz', '20250620_120003-work-site_config_backup-enc.json', '20250620_180003-work-database-enc.sql.gz', '20250619_180002-work-site_config_backup-enc.json', '20250620_120003-work-database-enc.sql.gz', '20250620_180003-work-site_config_backup-enc.json']
      this_file = '20250619_180002-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250619_180002-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250619_180002-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250621_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-21 12:00:10.467807
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250621_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250621_120003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7346445e1620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x734643925f00>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x734643925f00>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250621_120003-work-database-enc.sql.gz', '20250620_120003-work-site_config_backup-enc.json', '20250620_180003-work-database-enc.sql.gz', '20250620_120003-work-database-enc.sql.gz', '20250620_180003-work-site_config_backup-enc.json', '20250621_120003-work-site_config_backup-enc.json']
      this_file = '20250621_120003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250621_120003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250621_120003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250622_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-22 18:00:10.573968
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250622_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250622_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250623_000003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-23 00:00:06.767933
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250623_000003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250623_000003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250623_000003-work-database-enc.sql.gz

[Errno 2] No such file or directory: './work/private/backups/20250623_000003-work-database-enc.sql.gz.gpg' -> './work/private/backups/20250623_000003-work-database-enc.sql.gz'
Error occurred during encryption. Files are stored without encryption.
Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-23 00:00:06.769495
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250623_000003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250623_000003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250623_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-23 12:00:09.746974
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250623_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250623_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250623_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-23 12:00:09.832774
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250623_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250623_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250623_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-23 18:00:07.224446
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250623_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250623_180003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250623_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-23 18:00:07.311313
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250623_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250623_180003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250624_000002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-24 00:00:09.905725
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250624_000002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250624_000002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250624_000002-work-database-enc.sql.gz

[Errno 2] No such file or directory: './work/private/backups/20250624_000002-work-database-enc.sql.gz.gpg' -> './work/private/backups/20250624_000002-work-database-enc.sql.gz'
Error occurred during encryption. Files are stored without encryption.
Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-24 00:00:10.151925
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250624_000002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250624_000002-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250624_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-24 12:00:10.170598
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250624_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250624_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250624_120003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-24 12:00:10.244259
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250624_120003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250624_120003-work-database-enc.sql.gz 2.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x790fc8e01620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x790fca4de770>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x790fca4de770>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250624_000002-work-database-enc.sql.gz', '20250623_180003-work-site_config_backup-enc.json', '20250624_000002-work-site_config_backup-enc.json', '20250623_120003-work-database-enc.sql.gz', '20250624_120003-work-site_config_backup-enc.json', '20250624_120003-work-database-enc.sql.gz', '20250623_120003-work-site_config_backup-enc.json', '20250623_180003-work-database-enc.sql.gz']
      this_file = '20250623_120003-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250623_120003-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250623_120003-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250624_180003-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-24 18:00:07.446659
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250624_180003-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250624_180003-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
Backup failed for Site work. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 865, in backup
    odb = scheduled_backup(
      context = CliCtxObj(sites=['work'], force=False, profile=False, verbose=True)
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x78bf14ee9620>
      exit_code = 0
      rollback_callback = None
      site = 'work'
  File "apps/frappe/frappe/utils/backups.py", line 589, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x78bf13c25d80>
  File "apps/frappe/frappe/utils/backups.py", line 625, in new_backup
    delete_temp_backups()
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x78bf13c25d80>
  File "apps/frappe/frappe/utils/backups.py", line 662, in delete_temp_backups
    os.remove(this_file_path)
      older_than = 24
      backup_path = './work/private/backups'
      file_list = ['20250624_000002-work-database-enc.sql.gz', '20250623_180003-work-site_config_backup-enc.json', '20250624_180003-work-site_config_backup-enc.json', '20250624_000002-work-site_config_backup-enc.json', '20250624_120003-work-site_config_backup-enc.json', '20250624_120003-work-database-enc.sql.gz', '20250623_180003-work-database-enc.sql.gz', '20250624_180003-work-database-enc.sql.gz']
      this_file = '20250624_000002-work-database-enc.sql.gz'
      this_file_path = './work/private/backups/20250624_000002-work-database-enc.sql.gz'
builtins.FileNotFoundError: [Errno 2] No such file or directory: './work/private/backups/20250624_000002-work-database-enc.sql.gz'
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250625_120002-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-06-25 12:00:09.810421
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250625_120002-work-site_config_backup-enc.json 332.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250625_120002-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
/bin/sh: 1: /home/<USER>/anaconda3/bin/bench: not found
Traceback (most recent call last):
  File "/home/<USER>/.local/bin/bench", line 8, in <module>
    sys.exit(cli())
             ^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 135, in cli
    frappe_cmd()
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 201, in frappe_cmd
    os.execv(f, [f] + ["-m", "frappe.utils.bench_helper", "frappe"] + sys.argv[1:])
FileNotFoundError: [Errno 2] No such file or directory
/bin/sh: 1: /home/<USER>/anaconda3/bin/bench: not found
Traceback (most recent call last):
  File "/home/<USER>/.local/bin/bench", line 8, in <module>
    sys.exit(cli())
             ^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 135, in cli
    frappe_cmd()
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 201, in frappe_cmd
    os.execv(f, [f] + ["-m", "frappe.utils.bench_helper", "frappe"] + sys.argv[1:])
FileNotFoundError: [Errno 2] No such file or directory
/bin/sh: 1: /home/<USER>/anaconda3/bin/bench: not found
Traceback (most recent call last):
  File "/home/<USER>/.local/bin/bench", line 8, in <module>
    sys.exit(cli())
             ^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 135, in cli
    frappe_cmd()
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 201, in frappe_cmd
    os.execv(f, [f] + ["-m", "frappe.utils.bench_helper", "frappe"] + sys.argv[1:])
FileNotFoundError: [Errno 2] No such file or directory
/bin/sh: 1: /home/<USER>/anaconda3/bin/bench: not found
Traceback (most recent call last):
  File "/home/<USER>/.local/bin/bench", line 8, in <module>
    sys.exit(cli())
             ^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 135, in cli
    frappe_cmd()
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 201, in frappe_cmd
    os.execv(f, [f] + ["-m", "frappe.utils.bench_helper", "frappe"] + sys.argv[1:])
FileNotFoundError: [Errno 2] No such file or directory
/bin/sh: 1: /home/<USER>/anaconda3/bin/bench: not found
Traceback (most recent call last):
  File "/home/<USER>/.local/bin/bench", line 8, in <module>
    sys.exit(cli())
             ^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 135, in cli
    frappe_cmd()
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 201, in frappe_cmd
    os.execv(f, [f] + ["-m", "frappe.utils.bench_helper", "frappe"] + sys.argv[1:])
FileNotFoundError: [Errno 2] No such file or directory
/bin/sh: 1: /home/<USER>/anaconda3/bin/bench: not found
Traceback (most recent call last):
  File "/home/<USER>/.local/bin/bench", line 8, in <module>
    sys.exit(cli())
             ^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 135, in cli
    frappe_cmd()
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 201, in frappe_cmd
    os.execv(f, [f] + ["-m", "frappe.utils.bench_helper", "frappe"] + sys.argv[1:])
FileNotFoundError: [Errno 2] No such file or directory
/bin/sh: 1: /home/<USER>/anaconda3/bin/bench: not found
Traceback (most recent call last):
  File "/home/<USER>/.local/bin/bench", line 8, in <module>
    sys.exit(cli())
             ^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 135, in cli
    frappe_cmd()
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 201, in frappe_cmd
    os.execv(f, [f] + ["-m", "frappe.utils.bench_helper", "frappe"] + sys.argv[1:])
FileNotFoundError: [Errno 2] No such file or directory
/bin/sh: 1: /home/<USER>/anaconda3/bin/bench: not found
Traceback (most recent call last):
  File "/home/<USER>/.local/bin/bench", line 8, in <module>
    sys.exit(cli())
             ^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 135, in cli
    frappe_cmd()
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 201, in frappe_cmd
    os.execv(f, [f] + ["-m", "frappe.utils.bench_helper", "frappe"] + sys.argv[1:])
FileNotFoundError: [Errno 2] No such file or directory
/bin/sh: 1: /home/<USER>/anaconda3/bin/bench: not found
Traceback (most recent call last):
  File "/home/<USER>/.local/bin/bench", line 8, in <module>
    sys.exit(cli())
             ^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 135, in cli
    frappe_cmd()
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 201, in frappe_cmd
    os.execv(f, [f] + ["-m", "frappe.utils.bench_helper", "frappe"] + sys.argv[1:])
FileNotFoundError: [Errno 2] No such file or directory
/bin/sh: 1: /home/<USER>/anaconda3/bin/bench: not found
Traceback (most recent call last):
  File "/home/<USER>/.local/bin/bench", line 8, in <module>
    sys.exit(cli())
             ^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 135, in cli
    frappe_cmd()
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 201, in frappe_cmd
    os.execv(f, [f] + ["-m", "frappe.utils.bench_helper", "frappe"] + sys.argv[1:])
FileNotFoundError: [Errno 2] No such file or directory
/bin/sh: 1: /home/<USER>/anaconda3/bin/bench: not found
Traceback (most recent call last):
  File "/home/<USER>/.local/bin/bench", line 8, in <module>
    sys.exit(cli())
             ^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 135, in cli
    frappe_cmd()
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 201, in frappe_cmd
    os.execv(f, [f] + ["-m", "frappe.utils.bench_helper", "frappe"] + sys.argv[1:])
FileNotFoundError: [Errno 2] No such file or directory
/bin/sh: 1: /home/<USER>/anaconda3/bin/bench: not found
Traceback (most recent call last):
  File "/home/<USER>/.local/bin/bench", line 8, in <module>
    sys.exit(cli())
             ^^^^^
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 135, in cli
    frappe_cmd()
  File "/home/<USER>/.local/lib/python3.11/site-packages/bench/cli.py", line 201, in frappe_cmd
    os.execv(f, [f] + ["-m", "frappe.utils.bench_helper", "frappe"] + sys.argv[1:])
FileNotFoundError: [Errno 2] No such file or directory
/bin/sh: 1: /home/<USER>/anaconda3/bin/bench: not found
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250701_180001-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-07-01 18:00:03.594812
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250701_180001-work-site_config_backup-enc.json 368.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250701_180001-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
/bin/sh: 1: /home/<USER>/anaconda3/bin/bench: not found
set -o pipefail; /usr/bin/mariadb-dump --user=_a9a78d24ac870bfb --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _a9a78d24ac870bfb | /usr/bin/gzip >> ./work/private/backups/20250702_120001-work-database-enc.sql.gz

Backup encryption is turned on. Please note the backup encryption key.
Backup Summary for work at 2025-07-02 12:00:04.157934
Config  : /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250702_120001-work-site_config_backup-enc.json 368.0B
Database: /home/<USER>/Desktop/work-bench/sites/work/private/backups/20250702_120001-work-database-enc.sql.gz 1.0MiB
Backup for Site work has been successfully completed
